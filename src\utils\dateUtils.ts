export function getQuarterDates() {
    const now = new Date();
    const quarter = Math.floor(now.getMonth() / 3) + 1;
    const startMonth = (quarter - 1) * 3;
    const start = new Date(now.getFullYear(), startMonth, 1);
    const end = new Date(now.getFullYear(), startMonth + 3, 0);
    return { start, end };
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
export function formatDate(date: Date, _created_at?: string, _p0?: string): string {
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are zero-indexed
    const year = String(date.getFullYear()).slice(-2); // Get last two digits of the year
    return `${day}-${month}-${year}`;
} 