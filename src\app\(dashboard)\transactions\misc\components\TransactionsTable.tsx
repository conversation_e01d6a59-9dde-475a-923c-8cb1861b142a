"use client";


// import { useUserDetails } from "@/app/(dashboard)/dashboard/misc/api/UserDetails";
import FilterLogo from "@/app/icons/sidebar/FilterLogo";
import {
  Button,
  Card,
  Checkbox,
  Input,
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/core";
import Search from "@/components/icons/Search";

import React, { useState } from "react";
import { useTransactionsDetails } from "../api/TransactionsDetails";
import ExportLogo from "@/app/icons/sidebar/ExportLogo";

const transactions = [
  {
    id: "TXN-5432",
    type: "Airtime",
    amount: "₦325,000",
    status: "Success",
    channel: "Mobile App",
    customer: "<PERSON>",
    date: "Apr 28, 10:21AM",
    reference: "R-88UIK0A",
  },
  {
    id: "TXN-5432",
    type: "Fund Transfer",
    amount: "₦325,000",
    status: "Success",
    channel: "Mobile App",
    customer: "Kemi Adamu",
    date: "Apr 28, 10:21AM",
    reference: "R-88UIK0A",
  },
];

export default function TransactionsTable() {
  // const [transactionsDetails, setTransactionsDetails] = useState({
  //   transaction_id: "",
  // });
  const [selectedTransactions, setSelectedTransactions] = useState<number[]>(
    []
  );

  const toggleSelectAll = () => {
    if (selectedTransactions.length === transactions.length) {
      setSelectedTransactions([]);
    } else {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //@ts-expect-error
      setSelectedTransactions(transactions.map((t) => t.id));
    }
  };

  const toggleSelect = (id: number) => {
    if (selectedTransactions.includes(id)) {
      setSelectedTransactions(selectedTransactions.filter((t) => t !== id));
    } else {
      setSelectedTransactions([...selectedTransactions, id]);
    }
  };

  const { data: transactionsDetailsResponse } = useTransactionsDetails();
  console.log(transactionsDetailsResponse);

  return (
    <div className="border-[1px] rounded-[8px] border-[#E9E9E9] w-full  py-[20px]">
      <Tabs defaultValue="account" className="">
        <TabsList className="flex w-fit flex-cols">
          <TabsTrigger value="transactions" className="">
            <p className="text-[14px] font-normal active:text-[#032282] focus-visible:text-[#032282] active:border-b-2 focus-visible:border-b-2 active:border-[#032282] focus-visible:border-[#032282] px-1 rounded-[0px] ">
              All Transactions
            </p>
          </TabsTrigger>
          <TabsTrigger value="bills">
            <p className="text-[14px] font-normal text-[#4A4A68CC] px-1">
              Pending Transactions
            </p>
          </TabsTrigger>

        </TabsList>
        {/* <div className="flex gap-[20px] border-b border-[#E2E8F0CC] pb-[4px]">


        <Link href="#" className=" pl-[14px]">
          <p className="text-[14px] font-normal text-[#032282] border-b-2 border-t-0 border-y-0  border-[#032282] px-1 rounded-[0px] ">
            Transactions
          </p>
        </Link>
        <Link href="#" className="">
          <p className="text-[14px] font-normal text-[#4A4A68CC] px-1">Bills</p>
        </Link>
        <Link href="#" className="">
          <p className="text-[14px] font-normal text-[#4A4A68CC] px-1">
            Tickets
          </p>
        </Link>
        <Link href="#" className="">
          <p className="text-[14px] font-normal text-[#4A4A68CC] px-1">
            Activity Log
          </p>
        </Link>
      </div> */}
        {/* Transactions card */}
        <TabsContent value="transactions">
          <Card>
            <div className="flex pt-[24px]  px-[14px] justify-between">

              <div className="py-4 flex justify-between items-center flex-wrap">
                <div className="flex items-center gap-2">
                  <div className="relative   h-[44px] border-[0.4px] border-[#00000033] rounded-[8px] whitespace-nowrap">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-black h-4 w-  " />
                    <Input
                      placeholder="Search a transaction"
                      className="pl-9 bg-inherit  text-black focus:border-[#F9FAFB]"
                    />
                  </div>
                  <Button className="bg-white border-[0.4px] border-[#00000033] rounded-[8px] text-black hover:bg-[#F9FAFB]  h-[44px] gap-2 font-medium text-sm">
                    <FilterLogo />
                    Filter
                  </Button>
                </div>

                <div className="flex items-center gap-2">
                  <Button className="bg-white border-[0.4px] border-[#00000033] rounded-[8px] text-black hover:bg-[#F9FAFB] h-[44px] gap-2 font-medium text-sm">
                    <ExportLogo />
                    Export
                  </Button>
                </div>
              </div>
            </div>

            <div className="overflow-y-auto h-full pt-[13px] px-[15px]  no-scrollbar">
              <table className="w-full table-xs  table-pin-cols">
                <thead className="bg-[#F9FAFB] text-black">
                  <tr className="divide-x divide-[#E2E8F0]">
                    <th className="px-4 pl-10 py-3 text-left">
                      <Checkbox
                        checked={
                          selectedTransactions.length === transactions.length
                        }
                        onCheckedChange={toggleSelectAll}
                      />
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium">ID</th>
                    <th className="px-4 py-3 text-left text-sm font-medium">
                      Type
                    </th>

                    <th className="px-4 py-3 text-left text-sm font-medium">
                      Amount
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium">
                      Status
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium">
                      Channel
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium">
                      Customer
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium">
                      Date
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium">
                      Reference
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium">
                      Action
                    </th>
                  </tr>
                </thead>
                <tbody className="border divide-y d divide-[#E2E8F0] h-[60vh]  ">
                  {transactions.map((t) => {
                    //   transactionsDetailsResponse?.results?.map(
                    // ({
                    //   transaction_id,
                    //   type_of_transaction,
                    //   amount_of_transaction,
                    //   transaction_status,
                    //   transaction_channel,
                    //   date_created,
                    //   transaction_reference,
                    //   email,
                    // }) =>{

                    return (
                      <>
                        <tr
                          key={t.id}
                          className="divide-x divide-[#E2E8F0] hover:bg-[#F9FAFB] "
                        >
                          <td className="px-4 pl-10 py-1 ">
                            <Checkbox
                              checked={selectedTransactions.includes(
                                Number(t.id)
                                //   (transaction_id)
                              )}
                              // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                              //@ts-ignore
                              onCheckedChange={() => toggleSelect(t.id)}
                            />
                          </td>
                          <td className="px-4 py-1 text-sm opacity-85 ">
                            {/* {transaction_id} */}
                            {t.id}
                          </td>
                          <td className="px-4 py-1 text-sm opacity-85 ">
                            {/* {type_of_transaction} */}
                            {t.type}

                          </td>
                          <td className="px-4 py-1 text-sm opacity-85">
                            {/* {email} */}
                            {t.customer}
                          </td>
                          <td className="px-4 py-1 text-sm opacity-85">
                            {/* {"₦" + amount_of_transaction} */}
                            {t.amount}
                          </td>
                          <td className="px-4 py-1 text-sm opacity-85">
                            {/* {transaction_status} */}
                            {t.status}
                          </td>
                          <td className="px-4 py-1 text-sm opacity-85">
                            {/* {transaction_channel} */}
                            {t.channel}
                          </td>
                          <td className="px-4 py-1 text-sm opacity-85">
                            {/* {date_created} */}
                            {t.date}
                          </td>
                          <td className="px-4 py-1 text-sm opacity-85">
                            {/* {transaction_reference} */}
                            {t.reference}
                          </td>

                          <td className="px-4 py-1 text-sm">
                            <button
                              onClick={() => {
                                //   console.log("hghhjghjg", transaction_id, "m");
                                // setTransactionsDetails({
                                //   transaction_id: t.id,
                                // });
                              }}
                              className={`px-1 py-1 rounded-10 text-xs font-medium   `}
                            >
                              {/* <ActionPopover
                              transaction_id={
                                transactionsDetails.transaction_id
                              }
                            /> */}
                              {/* <ActionModal customer_id="" customer_name="" /> */}
                            </button>
                          </td>
                        </tr>
                      </>
                    );
                  }
                  )
                  }
                </tbody>
              </table>
            </div>
          </Card>
        </TabsContent>



        <TabsContent value="bills">
          {/* <Card>
        <div className="flex pt-[24px]  px-[14px] justify-between">

          <div className="flex items-center gap-2">
            <div className="relative  r h-[34px] border-[0.4px]  border-[#00000033] rounded-[8px] whitespace-nowrap">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-black h-4  " />
              <Input
                placeholder="Search a transaction"
                className=" pl-9 bg-inherit text-[12px] text-black h-[34px] focus:border-[#032282]"
              />
            </div>
            <Button className="bg-white border-[0.4px] border-[#00000033] rounded-[8px] text-black hover:bg-[#F9FAFB]  h-[34px] gap-2 font-medium text-sm">
              <FilterLogo />
              Filter
            </Button>
          </div>
        </div>

        <div className="overflow-y-auto h-full pt-[13px] px-[15px]  no-scrollbar">
          <table className="w-full table-xs  table-pin-cols">
            <thead className="bg-[#F9FAFB] text-black">
              <tr className="divide-x divide-[#E2E8F0]">
                <th className="px-4 pl-10 py-3 text-left">
                  <Checkbox
                    checked={
                      selectedTransactions.length === transactions.length
                    }
                    onCheckedChange={toggleSelectAll}
                  />
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium">ID</th>
                <th className="px-4 py-3 text-left text-sm font-medium">
                  Type
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium">
                  Biller
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium">
                  Amount
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium">
                  Status
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium">
                  Date
                </th>
                <th className="px-4 py-3 text-left text-sm font-medium">
                  Channel
                </th>

                <th className="px-4 py-3 text-left text-sm font-medium">
                  Action
                </th>
              </tr>
            </thead>
            <tbody className="border divide-y d divide-[#E2E8F0] h-[60vh]  ">
              {transactionsDetailsResponse?.results?.map(
                ({
                  transaction_id,
                  type_of_transaction,
                  amount_of_transaction,
                  transaction_status,
                  transaction_channel,
                  date_created,
                  transaction_reference,

                }) => {
                  return (
                    <>
                      <tr
                        key={transaction_id}
                        className="divide-x divide-[#E2E8F0] hover:bg-[#F9FAFB] "
                      >
                        <td className="px-4 pl-10 py-1">
                          <Checkbox
                            checked={selectedTransactions.includes(
                              Number(transaction_id)
                            )}
                            onCheckedChange={() => toggleSelect(transaction_id)}
                          />
                        </td>
                        <td className="px-4 py-1 text-sm opacity-85">
                          {transaction_id}
                        </td>
                        <td className="px-4 py-1 text-sm opacity-85 ">
                          {type_of_transaction}
                        </td>
                        <td className="px-4 py-1 text-sm opacity-85">
                          biller
                        </td>
                        <td className="px-4 py-1 text-sm opacity-85">
                          {"₦" + amount_of_transaction}
                        </td>
                        <td className="px-4 py-1 text-sm opacity-85">
                          {transaction_status}
                        </td>
                        <td className="px-4 py-1 text-sm opacity-85">
                          {date_created}
                        </td>
                        <td className="px-4 py-1 text-sm opacity-85">
                          {transaction_channel}
                        </td>


                        <td className="px-4 py-1 ">
                          <button
                            onClick={() => {
                              console.log("hghhjghjg", transaction_id, "m");
                              setTransactionsDetails({
                                transaction_id: transaction_id,
                              });
                            }}
                            className={`px-1 py-1 rounded-10 text-xs font-medium   `}
                          >
                            <ActionPopover
                              transaction_id={
                                transactionsDetails.transaction_id
                              }
                            />

                          </button>
                        </td>
                      </tr>
                    </>
                  );
                }
              )}
            </tbody>
          </table>
        </div>
        </Card> */}
        </TabsContent>
      </Tabs>
    </div>
  );
}
