"use client";
import React from "react";
// import Image from "next/image";
// import OtpInput from "react-otp-input";
import { useRouter } from "next/navigation";
// import PinInput from "react-pin-input";


export const OtpForm = () => {
//   const [otp, setOtp] = useState("");
  const router = useRouter();


  // const [passcode, setPasscode] = React.useState('');


  // const handleComplete = (passcode: string) => {
  //   setPasscode(passcode);
  //   handleOTPValidation(passcode);
  // };

  // const handleOTPValidation = (passcode: string) => {
  //   console.log("OTP entered:", passcode);
  //     };

  
  return (
    <div className=" flex items-center justify-center w-full h-[500px]  sm:h-[384px]   rounded-[20px]">
      <div className="flex flex-col items-start justify-start w-full  ">
        {/* <OtpInput
          value={otp}
          onChange={setOtp}
          //   renderSeparator={<span>-</span>}
          numInputs={6}
          renderInput={(props) => <input {...props} />}
          inputStyle="inputStyle"
          
          //   containerStyle={true}
        /> */}

{/* <PinInput 
  length={6} 
  initialValue=""
  secret
  secretDelay={100} 
  onChange={(value, index) => {}} 
  type="numeric" 
  inputMode="number"
  style={{padding: '10px'}}  
  inputStyle={{borderColor: 'red'}}
  inputFocusStyle={{borderColor: 'blue'}}
  onComplete={(value, index) => {}}
  autoSelect={true}
  regexCriteria={/^[ A-Za-z0-9_@./#&+-]*$/}
/> */}


{/* 
<PinInput
          autoSelect={false}
          initialValue="o"
          inputFocusStyle={{
            border: '3px solid #403C3A',
            borderRadius: '.625rem',
            padding: '0.5rem',
            outline: 'none',
            color: 'white',
            background: 'rgba(255, 255, 255, 0.3)',
            boxShadow: '0 0 0 1px rgb(255, 255, 255)',
          }}
          inputMode="number"
          inputStyle={{
            marginRight: '.3125rem',
            marginLeft: '.3125rem',
            background: '#F5F7F9',
            borderRadius: '14px',
            fontSize: '15px',
            fontWeight: '500',
            width: 'calc(16.5% - 10px)',
            height: 'unset',
            aspectRatio: '1',
            transitionDuration: '300ms',
            transitionProperty:
              'color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter',
            transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)',
          }}
          length={6}
          style={{ maxWidth: '25rem' }}
          type="numeric"
          onComplete={handleComplete}
        /> */}

  

        
        <div className="flex justify-between items-center mt-[8px] ">
          

          <div className="flex gap-[3px]  items-center  justify-start text-[12px] font-medium text-[#10192866]">
            <button className="cursor-pointer ">Resend OTP in</button>
          </div>

          <div className="flex px-2  items-center text-[#032282] text-[14px] font-medium">
            <p >04:52</p>
          </div>
        </div>

        <button
          className="flex border-[0.5px] bg-[#032282] opacity-60 hover:bg-[#032282] hover:opacity-100 justify-center items-center py-1.5 mt-[32px] rounded-[10px] text-white text-[16px] w-full h-[56px] cursor-pointer"
          onClick={() => {
            router.push("/");
          }}
        >
          Verify and continue
        </button>
      </div>
    </div>
  );
};
