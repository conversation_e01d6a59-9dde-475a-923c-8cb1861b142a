'use client'
import { Checkbox } from "@/components/core";
import React from "react";



export default function TransactionsOverview() {

    // const [selectedTransactions, setSelectedTransactions] = useState<number[]>([])

    // const toggleSelectAll = () => {
    //     if (selectedTransactions.length === cardDetails2.length) {
    //       setSelectedTransactions([])
    //     } else {
    //       setSelectedTransactions(cardDetails2.map((t) => t.id))
    //     }
    //   }
    
    //   const toggleSelect = (id: number) => {
    //     if (selectedTransactions.includes(id)) {
    //       setSelectedTransactions(selectedTransactions.filter((t) => t !== id))
    //     } else {
    //       setSelectedTransactions([...selectedTransactions, id])
    //     }
    //   }

    const cardDetails2 = [
        {
          title: "Total Volume",
          amount: "470,483",
        },
        {
          title: "Total Transactions",
          amount: "470,483",
        },
        {
          title: "Successful Transactions",
          amount: "470,483",
        },
        {
          title: "Failed Transactions",
          amount: "470,483",
        },
        {
          title: "Pending Resolutions",
          amount: "470,483",
        },
      ];


  return (
    <div className="pb-3  ">
      <div className="flex justify-between items-center text-black  px-6 pt-6 gap-4 flex-wrap">
        <span className=" text-2xl font-semibold">
          Transactions{" "}
          <p className="text-[14px] font-normal text-[#667085]">
            Showing data over the last 30 days
          </p>
        </span>
      </div>

      <div className="grid justify-items-center grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-y-4 gap-x-4  py-3 px-6  ">
        {cardDetails2.map((card, index) => (
          <div
            key={index}
            className="flex  border-1 border-[#E9EBEE]  rounded-[12px] w-full  pb-[21.5px]  py-2"
          >
            <div className="flex items-center justify-center w-[32px] h-[32px] rounded-full  ml-4 mt-4">
                <Checkbox className="rounded-full size-[32px] border-[#E9E9E9]"
                //  checked={selectedTransactions.length === transactions.length}
                //  onCheckedChange={toggleSelectAll}
                  />
            </div>
            <div>
                <h1 className="pt-4 px-4 font-normal text-xs text-[#4A4A68] ">
              {card.title}
            </h1>
            <h1 className="py-2 px-4 font-normal text-[18px] text-">
              {" "}
              {card.amount}
            </h1></div>
            
          </div>
        ))}
      </div>
    </div>
  );
}
