// components/ui/modal-loader.tsx
'use client';

import { Dialog, DialogContent } from "@/components/ui/dialog";
import { Loader2 } from "lucide-react";

interface ModalLoaderProps {
  isLoading: boolean;
  message?: string;
}

export const ModalLoader: React.FC<ModalLoaderProps> = ({ isLoading, message = "Loading..." }) => {
  return (
    <Dialog open={isLoading}>
      <DialogContent className="flex flex-col items-center justify-center gap-4 py-8">
        <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
        <p className="text-center text-sm font-medium">{message}</p>
      </DialogContent>
    </Dialog>
  );
};
export default ModalLoader;