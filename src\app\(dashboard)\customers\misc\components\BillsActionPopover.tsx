import DotLogo from '@/app/icons/sidebar/DotLogo'
import { Button,  Popover, PopoverContent, PopoverTrigger } from '@/components/core'
import Link from 'next/link'

import React from 'react'
import { BillDetailsModal } from './BillDetailsModal';
interface FetchBillDetails{
    bill_id: string;
    customer_name?: string
}

export default function BillsActionPopover({bill_id}:FetchBillDetails) {
    console.log(bill_id, "jbjnjn")
  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="light">
            <DotLogo />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[201px] rounded-[12px] p-0">
        <div className="flex flex-col  text-black font-normal">
          <div className="flex items-center border-b-[0.4px] p-[14px]">
          {/* <Link href={`/customers/customer-details/${customer_id}`}> */}
          <BillDetailsModal bill_id={bill_id}/>
          {/* </Link> */}
          </div>
          
            <div className="flex items-center border-b-[0.4px] p-[14px] ">
              
            <Link href="/dashboard">
          <p className="text-sm text-[#00000066]">Resolve Customer</p>
          </Link>
            </div>
            <div className="flex items-center p-[14px] ">
              
            <Link href="/dashboard">
          <p className="text-sm ">Suspend Customer</p>
          </Link>
            </div>
            
           
          </div>
        
      </PopoverContent>
    </Popover>
  )
}

