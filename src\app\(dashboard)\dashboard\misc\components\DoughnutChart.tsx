"use client"

import React from 'react'
import { defaults } from 'chart.js/auto';
import { Doughnut } from 'react-chartjs-2';
import { ProductUsageChartData } from '../api/ProductUsageChart';

defaults.maintainAspectRatio = false;
defaults.responsive = true;

interface DoughnutChartProps {
  data?: ProductUsageChartData;
  isLoading?: boolean;
  description?: string;
}

export default function DoughnutChart({ data: productUsageData, isLoading, description = "Today's Transactions" }: DoughnutChartProps) {

  // Process the real data or use fallback
  const processedData = React.useMemo(() => {
    if (!productUsageData?.transaction_types) {
      return {
        labels: ['No Data'],
        datasets: [{
          label: 'Count',
          data: [1],
          backgroundColor: ['#E5E7EB'],
          borderColor: ['#E5E7EB'],
          borderWidth: 1,
        }],
      };
    }

    // Get top 4 transaction types and group the rest as "Others"
    const sortedTypes = [...productUsageData.transaction_types]
      .sort((a, b) => b.count - a.count);

    const top4 = sortedTypes.slice(0, 4);
    const others = sortedTypes.slice(4);

    const labels = top4.map(type => {
      // Simplify transaction type names for display
      const name = type.transaction_type.replace(/_/g, ' ').toLowerCase();
      return name.charAt(0).toUpperCase() + name.slice(1);
    });

    const data = top4.map(type => type.count);

    // Add "Others" if there are more than 4 types
    if (others.length > 0) {
      const othersCount = others.reduce((sum, type) => sum + type.count, 0);
      labels.push('Others');
      data.push(othersCount);
    }

    const colors = ['#032282', '#2D4696', '#4F64A7', '#475467', '#6B7280'];

    return {
      labels,
      datasets: [{
        label: 'Count',
        data,
        backgroundColor: colors.slice(0, labels.length),
        borderColor: colors.slice(0, labels.length),
        borderWidth: 1,
      }],
    };
  }, [productUsageData]);

  const options = {
    cutout: '75%',
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        enabled: true,
      },
    },
  };

  if (isLoading) {
    return (
      <div className="w-full h-full flex flex-col">
        <div className="relative h-48 flex justify-center items-center">
          <div className="animate-pulse bg-gray-300 rounded-full w-32 h-32"></div>
          <div className="absolute flex flex-col items-center justify-center">
            <div className="animate-pulse bg-gray-300 h-4 w-20 mb-2 rounded"></div>
            <div className="animate-pulse bg-gray-300 h-8 w-16 rounded"></div>
          </div>
        </div>
        <div className="grid grid-cols-2 gap-2 mt-4">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="flex items-center gap-2">
              <div className="animate-pulse bg-gray-300 w-3 h-3 rounded-full"></div>
              <div className="animate-pulse bg-gray-300 h-4 w-20 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full flex flex-col">
      <div className="relative h-48 flex justify-center items-center">
        <Doughnut data={processedData} options={options} />
        <div className="absolute flex flex-col items-center justify-center">
          <p className="text-xs text-gray-500">{description}</p>
          <p className="text-3xl font-bold">
            {productUsageData?.total_transactions?.toLocaleString() || '0'}
          </p>
        </div>
      </div>
      <div className="grid grid-cols-2 gap-2 mt-4">
        {processedData.labels.map((label, index) => {
          const percentage = processedData.datasets[0].data[index];
          const total = processedData.datasets[0].data.reduce((sum, val) => sum + val, 0);
          const percentageValue = total > 0 ? ((percentage / total) * 100).toFixed(1) : '0';
          const color = processedData.datasets[0].backgroundColor[index];

          return (
            <div key={index} className="flex items-center gap-2">
              <span
                className="inline-block w-3 h-3 rounded-full"
                style={{ backgroundColor: color }}
              ></span>
              <span className="text-sm">{label}: {percentageValue}%</span>
            </div>
          );
        })}
      </div>
    </div>
  )
}
