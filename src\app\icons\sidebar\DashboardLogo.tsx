import { ActiveElement } from "chart.js";
import * as React from "react";
import { SVGProps } from "react";

// const DashboardLogo = (props: SVGProps<SVGSVGElement> ) => (
  const DashboardLogo = ({activeState} ) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill={activeState ? "#9ca3af" : "#ffffff"}
    xmlns="http://www.w3.org/2000/svg"
    // {...props}
  >
    <path
      d="M14.825 18.9584H5.17502C2.89169 18.9584 1.04169 17.1001 1.04169 14.8167V8.64173C1.04169 7.50839 1.74169 6.08339 2.64169 5.38339L7.13335 1.88339C8.48335 0.833393 10.6417 0.783393 12.0417 1.76673L17.1917 5.37506C18.1834 6.06673 18.9584 7.55006 18.9584 8.75839V14.8251C18.9584 17.1001 17.1084 18.9584 14.825 18.9584ZM7.90002 2.86673L3.40835 6.36673C2.81669 6.83339 2.29169 7.89173 2.29169 8.64173V14.8167C2.29169 16.4084 3.58335 17.7084 5.17502 17.7084H14.825C16.4167 17.7084 17.7084 16.4167 17.7084 14.8251V8.75839C17.7084 7.95839 17.1334 6.85006 16.475 6.40006L11.325 2.79173C10.375 2.12506 8.80835 2.15839 7.90002 2.86673Z"
      fill="white"
    />
    <path
      d="M10 15.625C9.65833 15.625 9.375 15.3417 9.375 15V12.5C9.375 12.1583 9.65833 11.875 10 11.875C10.3417 11.875 10.625 12.1583 10.625 12.5V15C10.625 15.3417 10.3417 15.625 10 15.625Z"
      fill="white"
    />
  </svg>
);
export default DashboardLogo;
