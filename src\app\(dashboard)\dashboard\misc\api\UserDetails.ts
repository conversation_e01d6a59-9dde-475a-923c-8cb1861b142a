import { adminAxios } from "@/lib/axios";
import {  useQuery } from "@tanstack/react-query";
// import axios from "axios"



export const getUserDetails = async () => {
    const response =await adminAxios.get(
        "/customer_care_management_system/customers_profile"

    );
    return response.data;
};


export const useUserDetails = () => {
    return useQuery({
        queryKey: ['kack'], 
        queryFn: () => getUserDetails(),
    
    });
}