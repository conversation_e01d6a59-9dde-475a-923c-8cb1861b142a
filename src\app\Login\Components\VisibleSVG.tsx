import * as React from "react";
import { SVGProps } from "react";
const VisibleSVG = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={20}
    height={20}
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M0.833344 9.62965C0.833344 9.62965 4.16668 3.2099 10 3.2099C15.8333 3.2099 19.1667 9.62965 19.1667 9.62965C19.1667 9.62965 15.8333 16.0494 10 16.0494C4.16668 16.0494 0.833344 9.62965 0.833344 9.62965Z"
      stroke="black"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10 12.037C11.3807 12.037 12.5 10.9592 12.5 9.62964C12.5 8.30006 11.3807 7.22223 10 7.22223C8.61929 7.22223 7.5 8.30006 7.5 9.62964C7.5 10.9592 8.61929 12.037 10 12.037Z"
      stroke="black"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default VisibleSVG;
