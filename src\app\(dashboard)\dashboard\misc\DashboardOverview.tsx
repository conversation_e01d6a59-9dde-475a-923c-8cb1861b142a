import React, { useState } from 'react';
import { Tabs, Tabs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { useDashboard, getApiPeriod, type DashboardPeriod } from './api/Dashboard';


export const DasboardOverview = () => {
  // State to track the selected period
  const [selectedPeriod, setSelectedPeriod] = useState<string>('today');

  // Convert tab value to API period and fetch data
  const apiPeriod = getApiPeriod(selectedPeriod);
  const { data: dashboardResponse, isLoading } = useDashboard(apiPeriod);

  console.log('Selected period:', selectedPeriod);
  console.log('API period:', apiPeriod);
  console.log('Dashboard response:', dashboardResponse?.data);
  console.log('Dashboard status:', dashboardResponse?.status);

  // Handle tab change
  const handleTabChange = (value: string) => {
    setSelectedPeriod(value);
  };

  // Get description text based on selected period
  const getDescriptionText = (period: string) => {
    switch (period) {
      case 'today':
        return 'Showing data for today';
      case 'this-week':
        return 'Showing data for this week';
      case 'this-month':
        return 'Showing data for this month';
      case 'all-time':
        return 'Showing all-time data';
      default:
        return 'Showing data for today';
    }
  };


  const cardDetails = [{
    title: 'Total Customer',
    amount: dashboardResponse?.data?.total_customers,
    percentage: dashboardResponse?.data?.total_customers_percentage_diff + "%",
    increase: 'vs last month'
  }, {
    title: 'Verified Customers',
    amount: dashboardResponse?.data?.verified_customers,
    percentage: dashboardResponse?.data?.verified_customers_percentage_diff + "%",
    increase: 'vs last month'
  }, {
    title: 'Active Customers',
    amount: dashboardResponse?.data?.active_customers,
    percentage: dashboardResponse?.data?.active_customers_percentage_diff + "%",
    increase: 'vs last month'
  }, {
    title: 'Pending Verification',
    amount: dashboardResponse?.data?.pending_verification,
    percentage: dashboardResponse?.data?.pending_verification_percentage_diff + "%",
    increase: 'vs last month'
  },
  {
    title: 'Total Transactions Count',
    amount: dashboardResponse?.data?.total_transactions,
    percentage: dashboardResponse?.data?.total_transactions_percentage_diff + "%",
    increase: 'vs last month'
  }, {
    title: 'Pending Transactions (3 days)',
    amount: dashboardResponse?.data?.pending_transactions_within_3days,
    percentage: dashboardResponse?.data?.pending_transactions_percentage_diff + "%",
    increase: 'vs last month'
  },
    // {
    //     title: 'Total Complaints',
    //     amount: dashboardResponse?.data?,
    //     percentage: dashboardResponse?.data?,
    //     increase: 'vs last month'
    // },{
    //     title: 'Unresolved Complaints',
    //     amount: dashboardResponse?.data?,
    //     percentage: dashboardResponse?.data?,
    //     increase: 'vs last month'
    // }
  ]


  //   return (
  //     <div className='pb-3 border-1 border-[#E9E9E9] rounded-xl '>
  //         <div className='flex justify-between items-center text-black  px-6 pt-6 gap-4 flex-wrap'>
  //         <span className=' text-2xl font-semibold'>Dashboard <p className='text-[14px] font-normal text-[#667085]'>Showing data over the last 30 days</p></span>
  //         <div className="  ">
  //         <Tabs defaultValue="today" className='border-2 border-[#032282] rounded-[8px] text-[14px] text-[#032282] font-normal' >
  //   <TabsList className='p-0 h-fit'>
  //     <TabsTrigger value="today" className='text-[#032282] data-[state=active]:border-b-solid-underline border-r-2 border-r-[#032282] rounded-none data-[state=active]:shadow-none  data-[state=active]:text-white data-[state=active]:bg-[#032282] data-[state=active]:rounded-[4px]'>Today</TabsTrigger>
  //     <TabsTrigger value="this-week" className='text-[#032282] data-[state=active]:border-b-solid-underline border-r-2 border-r-[#032282] rounded-none data-[state=active]:shadow-none  data-[state=active]:text-white data-[state=active]:bg-[#032282] '>This week</TabsTrigger>
  //     <TabsTrigger value="this-month" className='text-[#032282] data-[state=active]:border-b-solid-underline border-r-2 border-r-[#032282] rounded-none data-[state=active]:shadow-none  data-[state=active]:text-white data-[state=active]:bg-[#032282] ' >This month</TabsTrigger>
  //     <TabsTrigger value="all-time" className='text-[#032282] data-[state=active]:border-b-solid-underline rounded-none data-[state=active]:shadow-none  data-[state=active]:text-white data-[state=active]:bg-[#032282] data-[state=active]:rounded-[4px] data-[state=active]:rounded-l-[0px]'>All time</TabsTrigger>
  //   </TabsList>
  //   {/* <TabsContent value="account">Make changes to your account here.</TabsContent>
  //   <TabsContent value="password">Change your password here.</TabsContent> */}
  // </Tabs>
  //         </div>



  //         </div>
  //         <div className='grid justify-items-center grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-y-4 gap-x-4  py-3 px-6  '>{
  //             cardDetails.map((card, index) => (
  //                 <div key={index} className='border-1 border-[#E9EBEE]  rounded-[12px] w-full  pb-[21.5px]  py-2'>
  //                 <h1 className='pt-4 px-4 font-normal text-xs text-[#4A4A68] '>{card.title}</h1>
  //                 <h1 className='py-2 px-4 font-normal text-[18px] text-'> {card.amount ||0}</h1>

  //                     <h3 className='pb- px-4 font-normal text-xs text-'>
  //                     <span className=' pr-2 text-xs text-[#069855]'>{(parseFloat(card.percentage).toFixed(1) ||0)}</span>
  //                         {card.increase}
  //                     </h3>

  //             </div>
  //             ))}


  //         </div>
  //     </div>

  //   );
  // };




  return (
    <div className='pb-3 border-1 border-[#E9E9E9] rounded-xl '>
      <div className='flex justify-between items-center text-black px-6 pt-6 gap-4 flex-wrap'>
        <span className='text-2xl font-semibold'>
          Dashboard
          <p className='text-[14px] font-normal text-[#667085]'>{getDescriptionText(selectedPeriod)}</p>
        </span>

        <Tabs value={selectedPeriod} onValueChange={handleTabChange} className='border-2 border-[#032282] rounded-[8px] text-[14px] text-[#032282] font-normal'>
          <TabsList className='p-0 h-fit'>
            <TabsTrigger value="today" className='text-[#032282] border-r-2 border-r-[#032282] data-[state=active]:text-white data-[state=active]:bg-[#032282] rounded-none data-[state=active]:rounded-[4px]'>Today</TabsTrigger>
            <TabsTrigger value="this-week" className='text-[#032282] border-r-2 border-r-[#032282] data-[state=active]:text-white data-[state=active]:bg-[#032282] rounded-none'>This week</TabsTrigger>
            <TabsTrigger value="this-month" className='text-[#032282] border-r-2 border-r-[#032282] data-[state=active]:text-white data-[state=active]:bg-[#032282] rounded-none'>This month</TabsTrigger>
            <TabsTrigger value="all-time" className='text-[#032282] data-[state=active]:text-white data-[state=active]:bg-[#032282] rounded-none data-[state=active]:rounded-[4px]'>All time</TabsTrigger>
          </TabsList>
        </Tabs>

      </div>

      <div className='grid justify-items-center grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-y-4 gap-x-4 py-3 px-6'>
        {isLoading ? (
          // ✅ Loading Skeleton
          [...Array(6)].map((_, index) => (
            <div key={index} className='animate-pulse border border-gray-200 rounded-[12px] w-full pb-5 p-4'>
              <div className='h-4 bg-gray-300 rounded w-1/2 mb-4'></div>
              <div className='h-6 bg-gray-300 rounded w-1/3 mb-4'></div>
              <div className='h-4 bg-gray-200 rounded w-2/3'></div>
            </div>
          ))
        ) : (
          // ✅ Actual Data
          cardDetails.map((card, index) => (
            <div key={index} className='border border-[#E9EBEE] rounded-[12px] w-full pb-[21.5px] py-2'>
              <h1 className='pt-4 px-4 font-normal text-xs text-[#4A4A68]'>{card.title}</h1>
              <h1 className='py-2 px-4 font-normal text-[18px]'>{card.amount || 0}</h1>
              <h3 className='px-4 font-normal text-xs'>
                <span className='pr-2 text-xs text-[#069855]'>
                  {parseFloat(card.percentage).toFixed(1) || 0}
                </span>
                {card.increase}
              </h3>
            </div>
          ))
        )}
      </div>
    </div>
  );
};