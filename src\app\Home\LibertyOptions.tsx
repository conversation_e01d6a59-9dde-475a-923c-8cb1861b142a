// import React from "react";
// import { libertyCoreData } from "@/data/data";
// import Image from "next/image";

// export default function LibertyOptions() {
//   return (
//     // Nav Links

//     <div className="grid grid-cols-3 gap-4 p-4">
//       {libertyCoreData.map((data) => {
//         return (
//             <div key={data.id} className="m-3">
//             <div className="relative h-[400px]"></div>
//           <link
//             href={data.url}
//             // key={data.id}
//             className="flex flex-col items-center justify-center bg-[#ffffff] border-b-1 border-[#042BA3] h-16 hover:bg-[#042BA3] hover:text-[#ffffff] text-[#042BA3] font-bold text-lg"
//           >
//             <Image
//               src={data.image}
//               alt={data.name}
//               width={67}
//               height={67}
//               className="h-full w-full object-cover rounded-lg"
//             />
//             <span>{data.name}</span>
//             <p>{data.description}</p>
//           </link>
//           </div>
//         );
//       })}
//     </div>
//   );
// }




import React from "react";
import { libertyCoreData } from "@/data/data";
import Image from "next/image";
import Link from "next/link";
// import ModalLoader from "../component/ui/ModalLoader";

export default function LibertyOptions() {

// const [isLoading, setIsLoading] = useState(false)

  return (
    
    // <div className="flex gap-10 md:py-10 md:px-60 py-4 px-4 items-center justify-center bg-gray-100 w-full flex-wrap mtb-10 ">
     <div className="grid xl:grid-cols-3 sm:grid-cols-2 grid-cols-1 items-center justify-center gap-6 md:h-[522px] md:overflow-auto h-full md:py py-4 px-4  bg-gray-100 no-scrollbar mb-8 ">   
      
      {/* {data ? <pre className="text-sm">{JSON.stringify(data, null, 2)}</pre> : <p>Waiting for data...</p>} */}

      {/* <ModalLoader isLoading={isLoading} message="Fetching data from server..." /> */}

      {libertyCoreData.map((data) => (
        <div key={data.id} className="m-3  ">
          {data.url && (
            <Link href={data.url}>
              <div className="flex flex-col items-center justify-center h-[240px] w-[240px] bg-[#ffffff] border border-[#D0D5DD]  hover:bg-gray-400 hover:text-[#ffffff] text-[#042BA3] font-bold text-lg rounded-lg p-4 cursor-pointer transition">
                <div className="relative w-[83px] h-[82.96px] mb-4">
                  <Image
                    src={data.image}
                    alt={data.name}
                    fill
                    className="object-fit rounded-lg"
                  />
                </div>
                
                <p className="text-center text-sm">{data.description}</p>
              </div>
            </Link>
          )}
        </div>
      ))}
    </div>
  );
}
