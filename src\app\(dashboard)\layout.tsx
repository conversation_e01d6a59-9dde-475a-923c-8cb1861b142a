'use client'

import type React from "react"
import { useState, useEffect } from "react"
import NextTopLoader from "nextjs-toploader"

import { DashboardHeader, QuickAccessModal, Sidebar } from "@/components/layout"

import { cn } from "@/utils/classNames"
// import SubscriptionNotificationBanner from './subscription/misc/components/banner/SubscriptionNotificationBanner';

import { SidebarProvider } from "@/components/layout/dashboard/SidebarContext";

export function ClientHideBottomPaddingWrapper({
  routes,
  children,
}: {
  routes: string[]
  children: (hideBottomPadding: boolean) => React.ReactNode
}) {
  const [hideBottomPadding, setHideBottomPadding] = useState(false)

  useEffect(() => {
    // Check if current path matches any of the routes
    const currentPath = window.location.pathname
    const shouldHideBottomPadding = routes.some((route) => currentPath.includes(route))
    setHideBottomPadding(shouldHideBottomPadding)
  }, [routes])

  return <>{children(hideBottomPadding)}</>
}

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const routesWithFullScreen = ["/instant-web/orders"]

  return (
    <>
      <NextTopLoader color="#426FFB" shadow={false} showSpinner={false} />
      <SidebarProvider>
        <div className="flex h-screen bg-[#101928]">
          {/* Sidebar - fixed height, does not scroll */}
          <aside className="hidden w-[250px] shrink-0 text-white md:block">
            <Sidebar />
          </aside>

          {/* Main container */}
          <div className="flex flex-col flex-1 overflow-hidden bg-dash-light-bg">
            {/* Header - fixed, does not scroll */}
            <div className="shrink-0">
              <DashboardHeader />
            </div>

            {/* Scrollable content */}
            <ClientHideBottomPaddingWrapper routes={routesWithFullScreen}>
              {(hideBottomPadding) => (
                <main
                  className={cn(
                    "flex-1 overflow-y-auto bg-white p-0 pb-32",
                    "[min-height:calc(100vh-4.875rem)] [min-height:calc(100svh-4.875rem)]",
                    "md:[min-height:calc(100vh-8.4rem)] md:[min-height:calc(100svh-8.4rem)]",
                    hideBottomPadding && "pb-0"
                  )}
                >
                  {children}
                </main>
              )}
            </ClientHideBottomPaddingWrapper>
          </div>
        </div>

        <QuickAccessModal />
      </SidebarProvider>
    </>
  )
}

