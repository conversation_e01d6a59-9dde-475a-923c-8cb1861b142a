"use client"
import React from "react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  Tooltip,
  
  ResponsiveContainer,
} from "recharts";
import { useTransactionVolumeChart } from "../api/TransactionVolumeChart";
import { Skeleton } from "@/components/core";

// const data = [
//   {
//     name: "Jan",
//     uv: 4000,
//     pv: 2400,
//     amt: 2400,
//   },
//   {
//     name: "Feb",
//     uv: 100,
//     pv: 100,
//     amt: 100000000,
//   },
//   {
//     name: "Mar",
//     uv: 100,
//     pv: 100,
//     amt: 2290,
//   },
//   {
//     name: "Apr",
//     uv: 100,
//     pv: 100,
//     amt: 2000,
//   },
//   {
//     name: "May",
//     uv: 100,
//     pv: 100,
//     amt: 2181,
//   },
//   {
//     name: "<PERSON>",
//     uv: 100,
//     pv: 100,
//     amt: 2500,
//   },
//   {
//     name: "Jul",
//     uv: 100,
//     pv: 100,
//     amt: 2100,
//   },
//     {
//         name: "Aug",
//         uv: 100,
//         pv: 100,
//         amt: 2000,
//     },
//     {
//         name: "Sep",
//         uv: 100,
//         pv: 100,
//         amt: 2100,
//     },
//     {
//         name: "Oct",
//         uv: 100,
//         pv: 100,
//         amt: 2000,
//     },
//     {
//         name: "Nov",
//         uv: 100,
//         pv: 100,
//         amt: 2100,
//     },
    
//     {
//         name: "Dec",
//         uv: 100,
//         pv: 100,
//         amt: 2100,
//     },
// ];

export default function TransactionTable2() {


const { data: transactionVolumeChartResponse, isLoading } = useTransactionVolumeChart();
  console.log(transactionVolumeChartResponse?.datasets, 'Sinmi')
  console.log(transactionVolumeChartResponse?.datasets[0].label, 'as')
  const labels = transactionVolumeChartResponse?.labels
  console.log(labels, "labels")

       // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //@ts-ignore
const transactionVolumeChartData =  labels?.map((data,i) =>( {
  months:data,
  successful:transactionVolumeChartResponse?.datasets[0].data[i],
  pending:transactionVolumeChartResponse?.datasets[1].data[i],
  failed:transactionVolumeChartResponse?.datasets[2].data[i],

}))
  console.log(transactionVolumeChartData, "transactionVolumeChartResponse")


if (isLoading) {
    return (
      <div className="w-full h-[336px] flex items-center justify-center">
        <Skeleton className="w-full h-full rounded-xl" />
      </div>
    );
  }


  return (
    <ResponsiveContainer width={'100%'} height={336}>
      <BarChart
      width={50}
        height={30}
        data={transactionVolumeChartData}
        margin={{
          top: 5,
          right: 30,
          left: 20,
          bottom: 5,
        }}
      >
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey = {"months"} />
        <YAxis />
        <Tooltip />
        <Bar
          dataKey= {"successful"}
          fill="#032282"
        //   activeBar={<Rectangle fill="pink" stroke="blue" />}
        />
        <Bar
          dataKey= {"pending"}
          fill="#4F64A7"
        //   activeBar={<Rectangle fill="gold" stroke="purple" />}
        />
        <Bar
          dataKey= {"failed"}
          fill="#2D4696"
          
        />
      </BarChart>
    </ResponsiveContainer>
  );
}
