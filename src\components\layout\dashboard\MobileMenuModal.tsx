'use client';

import * as React from 'react';
// import { signOut } from 'next-auth/react';

import {
  Button,
  Dialog,
  DialogBody,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  LogOutModal,
} from '@/components/core';

import { useBooleanStateControl } from '@/hooks';

import { linkGroups } from './Sidebar';
import {
  SidebarCollapsible,
  SidebarCollapsibleWithLink,
  SidebarLink,
} from './SidebarLink';

export function MobileMenuDialog() {

  const {
    state: isLogoutModalOpen,
    setState: setLogoutModalState,
    setTrue: openLogoutModal,
  } = useBooleanStateControl();

  const {
    state: isModalOpen,
    setState: setModalState,
  } = useBooleanStateControl();

  // useRouteChangeEvent(() => closeModal());

  return (
    <>
      <Dialog open={isModalOpen} onOpenChange={setModalState}>
        <DialogTrigger
          className="bg-[#2D4696] px-6 py-2 text-white md:hidden"
          size="unstyled"
          variant="light"
        >
          Menu
        </DialogTrigger>

        <DialogContent
          className="inset-x-0 bottom-auto top-5 mx-auto w-full max-w-[90%] rounded-[1.125rem] bg-dash-dark-bg data-[state=open]:slide-in-from-bottom-5 sm:fixed sm:my-0 sm:w-full sm:max-w-[90%] md:hidden"
          overlayClassName="md:hidden items-center justify-center"
        >
          <DialogHeader className="bg-transparent pb-1 pt-10">
            <DialogTitle className="text-white">Menu</DialogTitle>

            <DialogClose className="ml-auto bg-white/10 text-white hover:bg-white/20">
              Close
            </DialogClose>
          </DialogHeader>

          <DialogBody>
            <DialogDescription className="sr-only max-w-[11.8125rem] text-light-text">
              Liberty Pay Menu
            </DialogDescription>

            <nav>
              <ul className="text-white">
                {linkGroups.map(({ heading, key, links }) => {
                  return (
                    <li className="py-4 first-of-type:pt-0" key={key}>
                      <h2 className="mb-1 px-3 uppercase">{heading}</h2>

                      <ul className="space-y-1 md:space-y-2">
                     
                        {
                           // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                      //@ts-ignore
                        links.map(({ icon, link, text, nestedLinks }, index: number) => {
                          return (
                            <li key={text + index}>
                              {!!nestedLinks && !!link && (
                                <SidebarCollapsibleWithLink
                                  icon={icon}
                                  link={link}
                                  nestedLinks={nestedLinks}
                                  text={text}
                                />
                              )}

                              {!!nestedLinks && !link && (
                                <SidebarCollapsible
                                  icon={icon}
                                  nestedLinks={nestedLinks}
                                  text={text}
                                />
                              )}

                              {!nestedLinks && !!link && (
                                <SidebarLink
                                  icon={icon}
                                  link={link}
                                  text={text}
                                />
                              )}
                            </li>
                          );
                        })}
                      </ul>
                    </li>
                  );
                })}
              </ul>

              <div className='w-full mt-7'>
                <Button className='py-3 font-medium' size={'fullWidth'} variant={'red'} onClick={event => {
                  event.preventDefault();
                  openLogoutModal();
                }}>
                  Log out
                </Button>
              </div>
            </nav>
          </DialogBody>
        </DialogContent>
      </Dialog>


      <LogOutModal
        heading=""
        isLogOutModalOpen={isLogoutModalOpen}
        setLogOutModalState={setLogoutModalState}
        subheading={''}
      >
        <div className="mt-[30px] flex gap-[20px]">
          <Button
            className="mt-1 px-[40px]"
            size="fullWidth"
            variant="red"
     
          >
            Log out
          </Button>
        </div>
      </LogOutModal>
    </>
  );
}
