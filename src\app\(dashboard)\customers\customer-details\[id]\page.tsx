"use client"
import BasicInformation from "../../misc/components/BasicInformation"
import CustomerDetailsOverview from "../../misc/components/CustomerDetailsOverview"
import CustomersTransaction from "../../misc/components/CustomersTransaction"
import { use } from "react"

export default function CustomerDetails({
  params,
}: {
  params: Promise<{ id: string }>
}) {
  // Use the React 'use' hook to unwrap the Promise
  const { id } = use(params)

  console.log(id, "kkdsd sdf")
  return (
    <div className="bg-white px-6 pb-5 pt-6 md:px-7 md:py-0 lg:px-6">
      <CustomerDetailsOverview customer_id={id} />
      <div className="flex w-full gap-[12px] px-[24px] md:h-[90vh] overflow-hidden">
        <div className="">
          <BasicInformation customer_id={id} />
        </div>
        <div className="">
          <CustomersTransaction />
        </div>
      </div>
    </div>
  )
}
