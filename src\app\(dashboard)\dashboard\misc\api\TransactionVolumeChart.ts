import { adminAxios } from "@/lib/axios";
import {  useQuery } from "@tanstack/react-query";
// import axios from "axios"

export interface TransactionVolumeChartData {
  labels: string[]
  datasets: Dataset[]
}

export interface Dataset {
  label: string
  data: number[]
}




export const getTransactionVolumeChart = async () => {
    const response =await adminAxios.get(
        "/customer_care_management_system/transaction_volume"

    );
    return response.data;
};


export const useTransactionVolumeChart = () => {
    return useQuery({
        queryKey: ['oajvsjbvsjbvs'], 
        queryFn: () => getTransactionVolumeChart(),
    
    });
}