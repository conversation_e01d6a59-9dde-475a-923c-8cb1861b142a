'use client'

import * as React from 'react';



import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/core';
import { DasboardOverview } from './misc/DashboardOverview';
// import TransactionsTable from './misc/components/TransactionsTable';
import TransactionTable2 from './misc/components/TransactionTable2';
// import PieChart from './misc/components/PieChart1';
// import PieChart1 from './misc/components/PieChart1';
import ProductUsage from './misc/components/ProductUsage';
import { useUserDetails } from './misc/api/UserDetails';
import { useTransactionVolumeChart } from './misc/api/TransactionVolumeChart';

// const dashboardOptions = [
//   {
//     name: 'HR management',
//     icon: (
//       <svg
//         className="h-6 w-6 shrink-0"
//         fill="none"
//         height={44}
//         viewBox="0 0 44 44"
//         width={44}
//         xmlns="http://www.w3.org/2000/svg"
//       >
//         <circle cx={22} cy={22} fill="#032282" r={22} />
//         <path
//           d="M28.183 21.792h-2.85a.63.63 0 0 1-.624-.625v-5.825c0-.617.241-1.192.675-1.625a2.282 2.282 0 0 1 1.625-.675h.008a3.968 3.968 0 0 1 2.775 1.158A3.97 3.97 0 0 1 30.95 17v2.017c.009 1.658-1.108 2.775-2.767 2.775Zm-2.224-1.25h2.224c.967 0 1.526-.559 1.526-1.525V17c0-.717-.284-1.4-.792-1.917a2.745 2.745 0 0 0-1.9-.791h-.008c-.276 0-.542.108-.742.308-.2.2-.308.458-.308.742v5.2Z"
//           fill="#fff"
//           fillOpacity={0.867}
//         />
//         <path
//           d="M19.5 31.442c-.392 0-.759-.15-1.034-.434l-1.383-1.391a.205.205 0 0 0-.275-.017l-1.425 1.067a1.444 1.444 0 0 1-1.525.141c-.5-.25-.808-.75-.808-1.308V17c0-2.517 1.441-3.958 3.958-3.958h10a.63.63 0 0 1 .625.625.63.63 0 0 1-.625.625 1.04 1.04 0 0 0-1.041 1.041V29.5c0 .558-.309 1.058-.809 1.308a1.445 1.445 0 0 1-1.525-.141L22.208 29.6a.202.202 0 0 0-.266.017l-1.4 1.4c-.284.275-.65.425-1.042.425Zm-2.575-3.134c.383 0 .758.142 1.041.434l1.384 1.391c.05.05.116.059.15.059.033 0 .1-.009.15-.059l1.4-1.4a1.45 1.45 0 0 1 1.908-.125l1.417 1.059a.19.19 0 0 0 .216.016c.042-.025.117-.075.117-.183V15.333c0-.375.092-.733.25-1.041H17c-1.85 0-2.709.858-2.709 2.708v12.5c0 .117.075.167.117.192.05.025.133.041.217-.025L16.05 28.6c.258-.192.566-.292.875-.292Z"
//           fill="#fff"
//           fillOpacity={0.867}
//         />
//         <path
//           d="M22 20.125h-5a.63.63 0 0 1-.625-.625.63.63 0 0 1 .625-.625h5a.63.63 0 0 1 .625.625.63.63 0 0 1-.625.625Zm-.625 3.333h-3.75a.63.63 0 0 1-.625-.625.63.63 0 0 1 .625-.625h3.75a.63.63 0 0 1 .625.625.63.63 0 0 1-.625.625Z"
//           fill="#fff"
//           fillOpacity={0.867}
//         />
//       </svg>
//     ),
//     subText: 'Efficiently manage your workforce operations, handling everything from employee data and attendance to payroll, ensuring a streamlined and effective HR process.',
//     link: '/payroll',
//   },
//   {
//     name: 'Spend management',
//     icon: (
//       <svg
//         className="h-6 w-6 shrink-0"
//         fill="none"
//         height={44}
//         viewBox="0 0 44 44"
//         width={44}
//         xmlns="http://www.w3.org/2000/svg"
//       >
//         <circle cx={22} cy={22} fill="#032282" r={22} />
//         <path
//           d="M21.583 19.708h-7.916a.63.63 0 0 1-.626-.625.63.63 0 0 1 .625-.625h7.917a.63.63 0 0 1 .625.625.63.63 0 0 1-.625.625Zm-2.916 6.667H17a.63.63 0 0 1-.625-.625.63.63 0 0 1 .625-.625h1.667a.63.63 0 0 1 .625.625.63.63 0 0 1-.625.625Zm5.416 0H20.75a.63.63 0 0 1-.625-.625.63.63 0 0 1 .625-.625h3.333a.63.63 0 0 1 .625.625.63.63 0 0 1-.625.625Z"
//           fill="#fff"
//           fillOpacity={0.867}
//         />
//         <path
//           d="M26.633 29.708h-9.267c-3.316 0-4.325-1-4.325-4.283v-6.85c0-3.283 1.009-4.283 4.325-4.283h6.717a.63.63 0 0 1 .625.625.63.63 0 0 1-.625.625h-6.716c-2.617 0-3.076.45-3.076 3.033v6.842c0 2.583.459 3.033 3.075 3.033h9.259c2.616 0 3.075-.45 3.075-3.033v-3.4a.63.63 0 0 1 .625-.625.63.63 0 0 1 .625.625v3.4c.008 3.291-1 4.291-4.317 4.291Z"
//           fill="#fff"
//           fillOpacity={0.867}
//         />
//         <path
//           d="M24.933 21.533a1.17 1.17 0 0 1-.85-.341 1.22 1.22 0 0 1-.325-1.05l.167-1.184c.042-.3.225-.658.433-.866L27.45 15c1.225-1.225 2.292-.525 2.825 0 .525.525 1.225 1.592 0 2.825l-3.092 3.092a1.681 1.681 0 0 1-.866.433l-1.184.167a1.668 1.668 0 0 1-.2.016Zm3.925-5.966c-.166 0-.316.116-.525.316l-3.083 3.092a.624.624 0 0 0-.083.167L25 20.267l1.133-.159a.627.627 0 0 0 .167-.083l3.092-3.092c.391-.391.45-.608 0-1.05-.217-.216-.375-.316-.534-.316Z"
//           fill="#fff"
//           fillOpacity={0.867}
//         />
//         <path
//           d="M29.392 18.45a.536.536 0 0 1-.167-.025 3.434 3.434 0 0 1-2.367-2.367.633.633 0 0 1 .434-.775c.333-.091.675.1.775.434a2.21 2.21 0 0 0 1.508 1.508.624.624 0 0 1 .433.767.658.658 0 0 1-.616.458Z"
//           fill="#fff"
//           fillOpacity={0.867}
//         />
//       </svg>
//     ),
//     subText:
//       "Optimize your company's expenses through real- time tracking and analysis, gaining insights into spending patterns to empower informed financial decision - making.",
//     link: '/spend-management',
//   },
//   {
//     name: 'Stock and sales',
//     icon: (
//       <svg
//         className="h-6 w-6 shrink-0"
//         fill="none"
//         height={44}
//         viewBox="0 0 44 44"
//         width={44}
//         xmlns="http://www.w3.org/2000/svg"
//       >
//         <circle cx={22} cy={22} fill="#032282" r={22} />
//         <path
//           d="M27.833 30.958a3.95 3.95 0 0 1-3.341-1.85c-.384-.591-.6-1.3-.617-2.025a3.914 3.914 0 0 1 1.417-3.116 4.016 4.016 0 0 1 2.45-.925c1.083-.009 2.058.366 2.825 1.1a3.898 3.898 0 0 1 1.216 2.775 3.878 3.878 0 0 1-.533 2.058c-.2.35-.458.675-.767.95a3.855 3.855 0 0 1-2.575 1.033h-.075Zm0-6.666h-.058c-.617.016-1.2.233-1.683.633a2.677 2.677 0 0 0-.967 2.133c.008.492.158.975.417 1.384.508.816 1.391 1.316 2.341 1.266A2.675 2.675 0 0 0 29.642 29c.216-.192.391-.408.524-.642.25-.433.375-.916.367-1.408a2.685 2.685 0 0 0-.833-1.9 2.649 2.649 0 0 0-1.867-.758Z"
//           fill="#fff"
//           fillOpacity={0.867}
//         />
//         <path
//           d="M27.375 28.458a.626.626 0 0 1-.433-.175l-.842-.8a.621.621 0 0 1-.017-.883.621.621 0 0 1 .884-.017l.408.392 1.308-1.267a.621.621 0 0 1 .884.017.621.621 0 0 1-.017.883l-1.742 1.684a.673.673 0 0 1-.433.166ZM22 23.083a.62.62 0 0 1-.317-.083l-7.358-4.259a.627.627 0 0 1 .625-1.083l7.041 4.075 7-4.05a.622.622 0 0 1 .85.225c.175.3.067.683-.225.858L22.308 23a.656.656 0 0 1-.308.083Z"
//           fill="#fff"
//           fillOpacity={0.867}
//         />
//         <path
//           d="M22 30.633a.63.63 0 0 1-.625-.625V22.45a.63.63 0 0 1 .625-.625.63.63 0 0 1 .625.625v7.558a.63.63 0 0 1-.625.625Z"
//           fill="#fff"
//           fillOpacity={0.867}
//         />
//         <path
//           d="M22 30.958c-.733 0-1.467-.158-2.033-.483l-4.45-2.467c-1.209-.666-2.159-2.275-2.159-3.658v-4.717c0-1.383.95-2.983 2.159-3.658l4.45-2.467c1.133-.641 2.916-.641 4.058 0l4.45 2.467c1.208.667 2.158 2.275 2.158 3.658v4.717c0 .083 0 .15-.016.233-.042.217-.2.4-.409.467a.64.64 0 0 1-.616-.117c-.959-.833-2.442-.866-3.45-.058a2.687 2.687 0 0 0-.417 3.8.615.615 0 0 1-.175.95l-1.525.842c-.567.333-1.292.491-2.025.491Zm0-16.666c-.517 0-1.042.108-1.417.316l-4.45 2.467c-.808.442-1.508 1.642-1.508 2.558v4.717c0 .917.708 2.117 1.508 2.558l4.45 2.467c.759.425 2.084.425 2.842 0l.933-.517a3.892 3.892 0 0 1-.475-1.875c0-1.216.542-2.341 1.484-3.091 1.133-.909 2.75-1.1 4.025-.559v-3.716c0-.917-.709-2.117-1.509-2.559l-4.45-2.466c-.391-.192-.916-.3-1.433-.3Z"
//           fill="#fff"
//           fillOpacity={0.867}
//         />
//       </svg>
//     ),
//     subText: "Improve stock and sales efficiency with real-time tracking and analysis, making informed decisions for optimal business performance.",
//     link: '/sales',
//   },
//   {
//     name: 'Inventory management',
//     icon: (
//       <svg
//         className="h-6 w-6 shrink-0"
//         fill="none"
//         height={44}
//         viewBox="0 0 44 44"
//         width={44}
//         xmlns="http://www.w3.org/2000/svg"
//       >
//         <circle cx={22} cy={22} fill="#032282" r={22} />
//         <path
//           d="M13.666 19.5v-1.667c0-2.5 1.667-4.166 4.167-4.166h8.334c2.5 0 4.166 1.666 4.166 4.166V19.5m-16.667 5v1.667c0 2.5 1.667 4.166 4.167 4.166h8.334c2.5 0 4.166-1.666 4.166-4.166V24.5"
//           stroke="#fff"
//           strokeLinecap="round"
//           strokeLinejoin="round"
//           strokeMiterlimit={10}
//           strokeOpacity={0.867}
//           strokeWidth={1.5}
//         />
//         <path
//           d="M17.584 19.717 22 22.275l4.384-2.541M22 26.808v-4.541"
//           stroke="#fff"
//           strokeLinecap="round"
//           strokeLinejoin="round"
//           strokeOpacity={0.867}
//           strokeWidth={1.5}
//         />
//         <path
//           d="M20.967 17.242 18.3 18.725c-.6.333-1.1 1.175-1.1 1.867v2.825c0 .691.492 1.533 1.1 1.866l2.667 1.483c.567.317 1.5.317 2.075 0l2.666-1.483c.6-.333 1.1-1.175 1.1-1.866v-2.825c0-.692-.491-1.534-1.1-1.867l-2.666-1.483c-.575-.325-1.509-.325-2.075 0Z"
//           stroke="#fff"
//           strokeLinecap="round"
//           strokeLinejoin="round"
//           strokeOpacity={0.867}
//           strokeWidth={1.5}
//         />
//       </svg>
//     ),
//     subText: "Effectively manage and organize your inventory, optimizing stock levels, reducing excess, and improving order fulfilment for better overall control.",
//     link: '/stock',
//   },
//   {
//     name: 'Invoicing',
//     icon: (
//       <svg
//         className="h-6 w-6 shrink-0"
//         fill="none"
//         height={44}
//         viewBox="0 0 44 44"
//         width={44}
//         xmlns="http://www.w3.org/2000/svg"
//       >
//         <circle cx={22} cy={22} fill="#032282" r={22} />
//         <path
//           d="M19.5 30.333h5c4.166 0 5.833-1.666 5.833-5.833v-5c0-4.167-1.666-5.833-5.833-5.833h-5c-4.167 0-5.834 1.666-5.834 5.833v5c0 4.167 1.667 5.833 5.834 5.833ZM25.125 19.5h-6.25m6.25 5h-6.25"
//           stroke="#fff"
//           strokeLinecap="round"
//           strokeLinejoin="round"
//           strokeWidth={1.5}
//         />
//       </svg>
//     ),
//     subText: "Efficiently manage your workforce operations, handling everything from employee data and attendance to payroll, ensuring a streamlined and effective HR process.",
//     link: '/invoices',
//   },
//   {
//     name: 'Pay bills',
//     icon: (
//       <svg
//         className="h-6 w-6 shrink-0"
//         fill="none"
//         height={44}
//         viewBox="0 0 44 44"
//         width={44}
//         xmlns="http://www.w3.org/2000/svg"
//       >
//         <circle cx={22} cy={22} fill="#032282" r={22} />
//         <path
//           d="M21.825 21.9c-.45 0-.908-.083-1.267-.242l-4.916-2.183c-1.25-.558-1.433-1.308-1.433-1.717 0-.408.183-1.158 1.433-1.716l4.916-2.184c.726-.325 1.817-.325 2.542 0l4.925 2.184c1.242.55 1.434 1.308 1.434 1.716 0 .409-.184 1.159-1.434 1.717L23.1 21.658c-.367.167-.817.242-1.275.242Zm0-7.033c-.283 0-.558.041-.758.133l-4.917 2.183c-.508.234-.691.467-.691.575 0 .109.183.35.683.575l4.916 2.184c.4.175 1.125.175 1.526 0l4.925-2.184c.508-.225.691-.466.691-.575 0-.108-.183-.35-.691-.575L22.592 15a2.104 2.104 0 0 0-.767-.133Z"
//           fill="#fff"
//         />
//         <path
//           d="M22 26.242c-.317 0-.633-.067-.933-.2l-5.659-2.517c-.858-.375-1.533-1.417-1.533-2.358a.63.63 0 0 1 .625-.625.63.63 0 0 1 .625.625c0 .458.375 1.033.792 1.225l5.658 2.516c.267.117.575.117.85 0l5.658-2.516c.417-.184.792-.767.792-1.225a.63.63 0 0 1 .625-.625.63.63 0 0 1 .625.625c0 .941-.675 1.983-1.533 2.366l-5.659 2.517c-.3.125-.616.192-.933.192Z"
//           fill="#fff"
//         />
//         <path
//           d="M22 30.408a2.28 2.28 0 0 1-.933-.2l-5.659-2.516a2.588 2.588 0 0 1-1.533-2.367.63.63 0 0 1 .625-.625c.342 0 .625.292.625.633 0 .525.308 1.009.792 1.225l5.658 2.517c.267.117.575.117.85 0l5.658-2.517c.484-.216.792-.691.792-1.225a.63.63 0 0 1 .625-.625.63.63 0 0 1 .625.625c0 1.025-.6 1.95-1.533 2.367l-5.659 2.517c-.3.125-.616.191-.933.191Z"
//           fill="#fff"
//         />
//       </svg>
//     ),
//     subText: "Simplify bill payments with a centralized platform, allowing you to manage and schedule payments effortlessly while keeping track of your payment history.",
//     link: '/buy-airtime-and-utilities',
//   },
//   {
//     name: 'Send money',
//     icon: (
//       <svg
//         className="h-6 w-6 shrink-0"
//         fill="none"
//         height={44}
//         viewBox="0 0 44 44"
//         width={44}
//         xmlns="http://www.w3.org/2000/svg"
//       >
//         <circle cx={22} cy={22} fill="#032282" r={22} />
//         <path
//           d="m18.167 17.267 7.075-2.359c3.175-1.058 4.9.675 3.85 3.85l-2.358 7.075c-1.584 4.759-4.184 4.759-5.767 0l-.7-2.1-2.1-.7c-4.758-1.583-4.758-4.175 0-5.766Zm2.258 6.108 2.983-2.992"
//           stroke="#fff"
//           strokeLinecap="round"
//           strokeLinejoin="round"
//           strokeWidth={1.5}
//         />
//       </svg>
//     ),
//     subText: "Securely transfer funds with ease, providing instant and hassle-free money transfers while allowing you to conveniently track and manage transactions.",
//     link: '/send-money',
//   },
//   {
//     name: 'POS shop outlet',
//     icon: (
//       <svg
//         className="h-6 w-6 shrink-0"
//         fill="none"
//         height={44}
//         viewBox="0 0 44 44"
//         width={44}
//         xmlns="http://www.w3.org/2000/svg"
//       >
//         <circle cx={22} cy={22} fill="#032282" r={22} />
//         <path
//           d="M24.5 30.958h-5c-3.675 0-4.791-1.116-4.791-4.791v-8.334c0-3.675 1.116-4.791 4.791-4.791h5c3.675 0 4.792 1.116 4.792 4.791v8.334c0 3.675-1.117 4.791-4.792 4.791Zm-5-16.666c-2.983 0-3.541.566-3.541 3.541v8.334c0 2.975.558 3.541 3.541 3.541h5c2.983 0 3.542-.566 3.542-3.541v-8.334c0-2.975-.559-3.541-3.542-3.541h-5Z"
//           fill="#fff"
//         />
//         <path
//           d="M23.667 17.208h-3.334a.63.63 0 0 1-.624-.625.63.63 0 0 1 .625-.625h3.333a.63.63 0 0 1 .625.625.63.63 0 0 1-.625.625ZM22 28.55a1.917 1.917 0 1 1 0-3.834 1.917 1.917 0 0 1 0 3.834Zm0-2.592c-.366 0-.666.3-.666.667 0 .367.3.667.666.667.367 0 .667-.3.667-.667 0-.367-.3-.667-.667-.667Z"
//           fill="#fff"
//         />
//       </svg>
//     ),
//     subText: "Efficiently manage POS outlets with a comprehensive overview of device performance for seamless tracking and informed decision-making.",
//     link: '/pos-shop-outlet',
//   },
// ];

export default function Dashboard() {


  const { data: userDetailsResponse } = useUserDetails();
  console.log(userDetailsResponse)


  const { data: transactionVolumeChartResponse } = useTransactionVolumeChart();
  console.log(transactionVolumeChartResponse, 'Sinmi')

  return (
    <>
      <div className="mb-3 mt-1.5 bg-white px-6 pb-5 pt-6 md:px-7 md:py-3 md:pb-6 lg:px-11">
        <DasboardOverview />

        <div className="flex w-full mt-4 rounded-10 gap-4 flex-col lg:flex-row justify-between">
          {/* <TransactionsTable /> */}
          <div className=" rounded-lg border-1 border-[#E9E9E9] w-full md:basis-[70%]  p-6">
            <div className="flex justify-between items-center gap-2 md:gap-8 mb-6 flex-wrap px-6 ">
              <h2 className="text-lg font-medium">Transactions Volume</h2>
              <div className="flex items-center gap-4 lg:gap-5 flex-wrap ">
                <div className='flex items-center gap-2'>
                  <div className="flex items-center gap-2 ">
                    <span className="inline-block w-3 h-2 md:h-3 rounded-full bg-[#032282]"></span>
                    <span className="text-xs md:text-sm">Successful</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="inline-block w-3 h-2 md:h-3 rounded-full bg-[#4F64A7]"></span>
                    <span className="text-xs md:text-sm">Pending</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="inline-block w-3 h-2 md:h-3 rounded-full bg-[#2D4696]"></span>
                    <span className="text-xs md:text-sm">Failed</span>
                  </div>
                </div>
                <Select defaultValue="Set-Duration" >
                  <SelectTrigger className=" lg:w-52 border-1 border-[#00000033] text-xs md:text-sm">
                    <SelectValue placeholder="Set Duration" />
                  </SelectTrigger>
                  <SelectContent className="whitespace-nowrap">
                    <SelectItem value="Set-Duration" className="whitespace-nowrap" >Set Duration</SelectItem>
                    <SelectItem value="jan-dec-2024" className="whitespace-nowrap" >Jan 2024 - Jun 2024</SelectItem>
                    <SelectItem value="jul-dec-2024" className="whitespace-nowrap">Jul 2024 - Dec 2024</SelectItem>
                  </SelectContent>
                </Select>
              </div>



            </div>
            <TransactionTable2 />
          </div>
          <div className='flex w-full basis-[28%] gap-4'>
            <ProductUsage />
          </div>
        </div>

      </div>


    </>
  );
}
