'use client';

import { usePathname, useRouter } from 'next/navigation';
import { useState, useEffect } from 'react';
import { convertKebabAndSnakeToTitleCase } from '@/utils/strings';

const peculiarPageNames: { [key: string]: string } = {
  '/dashboard': 'Home',
  '/analytics': 'Analytics Dashboard',
  '/pos-shop-outlet': 'POS Shop Outlet',
  '/stock/product-and-categories/view': 'Categories & Products',
};

const pagesWithSecondLastHeadings = [
  '/company-expenses',
  '/company-requisitions',
  '/team-requisitions',
  '/team-expenses',
  '/spend-management/expenses/record-expense/options',
  '/instant-web/product-details/customize-product',
  '/instant-web/product-details/inventory',
  '/instant-web/product-details/orders',
  '/instant-web/product-details/post-purchase',
];

const pagesWithoutBackButton = [
  '/dashboard',
  '/analytics',
  '/send-money',
  '/savings',
  '/spend-management',
  '/spend-management/teams',
  '/spend-management/overview',
  '/spend-management/companies',
  '/spend-management/teams',
  '/profile-and-settings',
  '/cards',
  '/customers',
  '/bank-performance',
  '/stock',
  '/leaderboard',
  '/stock/companies',
  '/profile-settings/personal-information',
  '/payroll',
  '/leave-management/leave-dashboard',
  '/pos-shop-outlet',
  '/payroll/settings',
  '/stock/product-and-categories/view',
];

const pathsThatUseCompanyNameAsHeader = [
  'zzzzzzzz'
];

const InstantWebSegmentToHeadingMap: { [key: string]: string } = {
  'all-products': 'All Products',
  'add-product': 'Add New Product',
  'transactions': 'Transactions',
  'customers': 'Customers',
  'product-categories': 'Product Categories',
  'orders': 'Orders',
  'analytics': 'Analytics',
};

export function DashboardHeaderHeading() {
  const router = useRouter();
  const pathname = usePathname();
  const [companyName, setCompanyName] = useState<string | null>(null);

  useEffect(() => {
    // Safely access search params on the client side
    const searchParams = new URLSearchParams(window.location.search);
    setCompanyName(searchParams.get('companyName'));
  }, []);

  const isPageNamePeculiar = peculiarPageNames[pathname] !== undefined;

  const hasSecondLastHeading =
    pagesWithSecondLastHeadings.some(page => pathname.includes(page)) ||
    pagesWithSecondLastHeadings.includes(pathname);

  const isPageWithoutBackButton = pagesWithoutBackButton.includes(pathname);

  const pathSegments = pathname.split('/').filter(segment => segment);
  const instantWebSegments = pathSegments[0] == "instant-web" ? pathSegments[1] : "";

  const pathNameHeading = convertKebabAndSnakeToTitleCase(
    decodeURIComponent(
      hasSecondLastHeading
        ? pathname.split('/').at(-2) || ''
        : pathname.split('/').slice(-1)[0]
    )
  );

  const usesCompanyNameAsHeader =
    pathsThatUseCompanyNameAsHeader.some(page => pathname.includes(page) || pathname.startsWith(page)) ||
    pathsThatUseCompanyNameAsHeader.includes(pathname);

  const pageName =
    isPageNamePeculiar
      ? peculiarPageNames[pathname]
      : InstantWebSegmentToHeadingMap[instantWebSegments]
        ? InstantWebSegmentToHeadingMap[instantWebSegments]
        : usesCompanyNameAsHeader
          ? companyName
          : pathNameHeading;

  const handleClick = () => {
    if (!isPageWithoutBackButton) {
      router.back();
    }
  };

  return (
    <>
      <div className="flex min-w-0 items-center gap-4">
        {!isPageWithoutBackButton && (
          <button type="button" onClick={handleClick}>
            <span className="sr-only">Go back</span>
            <svg
              fill="none"
              height="42"
              viewBox="0 0 42 42"
              width="42"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect
                height="40"
                rx="20"
                stroke="#F4F4F6"
                strokeWidth="1.5"
                width="40"
                x="1"
                y="1"
              />
              <mask
                height="40"
                id="a"
                maskUnits="userSpaceOnUse"
                width="40"
                x="1"
                y="1"
              >
                <rect fill="#fff" height="40" rx="20" width="40" x="1" y="1" />
              </mask>
              <g mask="url(#a)">
                <path
                  className="fill-white md:fill-[#121826]"
                  clipRule="evenodd"
                  d="M18.172 23.828a4 4 0 0 1-.151-5.497l.15-.16 4.12-3.878a1 1 0 0 1 1.497 1.32l-.083.094-4.12 3.879a2 2 0 0 0-.116 2.701l.117.127 4.12 3.879a1 1 0 0 1-1.32 1.497l-.095-.083-4.12-3.879Z"
                  fill="#121826"
                  fillRule="evenodd"
                />
              </g>
            </svg>
          </button>
        )}

        <h1 className="min-w-0 shrink truncate font-wix-display text-xl font-medium text-white md:text-dark-text">
          {pageName || 'Paybox Dashboard'}
        </h1>
      </div>
    </>
  );
}
