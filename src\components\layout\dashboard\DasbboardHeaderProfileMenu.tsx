'use client';

// import { signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import * as React from 'react';
import { useIdle } from 'react-use';

import { useBooleanStateControl } from '@/hooks';

import {
  Avatar,
  AvatarFallback,
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  InActivityModal,
  // LoaderModal,
  LogOutModal,
  TokenExpiredModal,
} from '../../core';
import { useUser } from '@/app/Login/api/getAuthenticatedUser';

// import { formatAxiosErrorMessage } from '@/utils/errors';



export function DashboardHeaderProfileMenu() {
  // DEFINE INACTIVITY TIME I.E IDLE
  const isIdle = useIdle(10 * 60 * 1000); // 10 minutes in milliseconds
  const isIdleForFifteenMinutes = useIdle(15 * 60 * 1000); //15 minutes in milliseconds



  const {
    state: isLogoutModalOpen,
    setState: setLogoutModalState,
    // setTrue: openLogoutModal,
  } = useBooleanStateControl();

  const {
    state: isInActivityModalOpen,
    setState: setInActivityModalState,
    setTrue: openInActivityModal,
    setFalse: closeInactivityModal,
  } = useBooleanStateControl();

  const {
    state: isTokenExpiredModalOpen,
    setState: setTokenExpiredModalState,
    // setTrue: openTokenExpiredModal,
    // setFalse: closeTokenExpireModal,
  } = useBooleanStateControl();

const { data: user, isLoading } = useUser();
  const router = useRouter();
  const userData = user?.user_data


  // React.useEffect(() => {
  //   // FOR NOW BASICALLY CONDITION FOR MY AUTHENTICATION ERROR CHECK
  //   // IMPLEMENTATION MIGHT CHANGE LATER
  //   if (userDetailsResponse === undefined) {
  //     console.log('userDetailsResponse', userDetailsResponse)
  //   }
  // }, [])

  React.useEffect(() => {
    if (isIdle) {
      // Perform actions when inactivity is detected (e.g., log out the user)
      openInActivityModal();
    }
  }, [isIdle, openInActivityModal]);

  React.useEffect(() => {
    if (isIdleForFifteenMinutes) {
      router.push('/login');
    }
  }, [isIdleForFifteenMinutes, router]);

  return (
    <>
      {/* <LoaderModal isOpen={isLoaderModalOpen} /> */}

      <DropdownMenu>
        <DropdownMenuTrigger className="flex items-center gap-3 rounded-md transition duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 active:scale-90">
          <Avatar>
            <AvatarFallback>
              {!isLoading && (
                  <button className="rounded-full w-9 h-9 text-sm text-[#032282]] flex justify-center items-center shrink-0 bg-[#F0F2F5] ">
                    {`${userData?.first_name?.slice(0, 1) ?? ""}${userData?.last_name?.slice(0, 1) ?? ""}`}

                   </button>
                )}</AvatarFallback>
          </Avatar>

          
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="">
          <DropdownMenuItem
            className="cursor-pointer text-red-800 focus:bg-red-100/70"
 
          >
            Log out
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <LogOutModal
        heading=""
        isLogOutModalOpen={isLogoutModalOpen}
        setLogOutModalState={setLogoutModalState}
        subheading={''}
      >
        <div className="mt-[30px] flex gap-[20px]">
          <Button
            className="mt-1 px-[40px]"
            size="fullWidth"
            variant="red"
     
          >
            Log out
          </Button>
        </div>
      </LogOutModal>

      <InActivityModal
        heading=""
        isInActivityModalOpen={isInActivityModalOpen}
        setInActivityModalState={setInActivityModalState}
        subheading={''}
      >
        <div className="mt-[30px] flex gap-[20px]">
          <Button
            className="mt-1 whitespace-nowrap px-[40px]"
            size="fullWidth"
            variant="outlined"
     
          >
            Done for now
          </Button>

          <Button
            className="mt-1 px-[40px]"
            size="fullWidth"
            variant="red"
            onClick={() => {
              closeInactivityModal();
            }}
          >
            No not done
          </Button>
        </div>
      </InActivityModal>

      <TokenExpiredModal
        heading="Session expired"
        isTokenExpiredModalOpen={isTokenExpiredModalOpen}
        setTokenExpiredModalState={setTokenExpiredModalState}
        subheading="Your session has expired. Please log in again."
      >

      </TokenExpiredModal>
    </>
  );
}
