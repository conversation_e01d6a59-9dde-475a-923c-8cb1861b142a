import type { Metada<PERSON> } from "next";
// import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { DM_Sans } from "next/font/google"
import "./globals.css";
import { ReactQueryProvider } from "@/lib/reactQuery";
import { AuthProvider } from "@/context/authentication";



const DM_SANS = DM_Sans({ subsets: ["latin"] })

// const geistSans = Geist({
//   variable: "--font-geist-sans",
//   subsets: ["latin"],
// });

// const geistMono = Geist_Mono({
//   variable: "--font-geist-mono",
//   subsets: ["latin"],
// });

export const metadata: Metadata = {
  title: "Liberty Core",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={DM_SANS.className}>
        <ReactQueryProvider>
          <AuthProvider>
            {/* <ProtectedRouteGuard>
              <Suspense fallback={<></>}>
                <Wrapper > */}
                  {children}
                  {/* </Wrapper>
              </Suspense>
            </ProtectedRouteGuard> */}
          </AuthProvider>
        </ReactQueryProvider>
      </body>
    </html>
  );
}
