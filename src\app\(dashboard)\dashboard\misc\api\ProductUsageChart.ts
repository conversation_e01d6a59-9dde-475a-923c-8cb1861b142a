
import { adminAxios } from "@/lib/axios";

import { useQuery } from "@tanstack/react-query";

// Define the period type for Product Usage
export type ProductUsagePeriod = 'today' | 'this_week' | 'this_month' | 'six_months' | 'this_year' | 'all_time';

// Map select values to API parameter values
const periodMap: Record<string, ProductUsagePeriod> = {
  'today': 'today',
  'this-Week': 'this_week',
  'this-month': 'this_month',
  'six-months': 'six_months',
  'this-year':  'this_year',
  'all-time': 'all_time'
};





export interface TransactionType {
  transaction_type: string;
  count: number;
  percentage: number;
}

export interface DateRange {
  start_date: string | null;
  end_date: string;
}

export interface ProductUsageChartData {
  filter_type: string;
  date_range: DateRange;
  total_transactions: number;
  transaction_types: TransactionType[];
  most_used_transaction: string;
}




export const getProductUsageChart = async (period: ProductUsagePeriod = 'today') => {
    const response = await adminAxios.get(
        `/customer_care_management_system/product_usage/?period=${period}`
    );
    console.log('Product Usage data for period:', period);
    return response.data;
};

export const useProductUsageChart = (period: ProductUsagePeriod = 'today') => {
    return useQuery({
        queryKey: ['product-usage', period],
        queryFn: () => getProductUsageChart(period),
    });
};

// Helper function to convert select value to API period
export const getApiPeriod = (selectValue: string): ProductUsagePeriod => {
    return periodMap[selectValue] || 'today';
};