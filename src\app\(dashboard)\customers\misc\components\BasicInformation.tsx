"use client";

import <PERSON>py<PERSON><PERSON> from "@/app/icons/sidebar/CopyLogo";
import Link from "next/link";
import React from "react";
import { useCustomerProfileDetails } from "../api/CustomerProfileDetails";
import { CreateAccountModal } from "./CreateAccountModal";




interface CustomerDetails {
  customer_id: string
}




export default function BasicInformation({ customer_id }: CustomerDetails) {

  const FetchCustomerProfile = {
    user_id: customer_id,
  }

  const { data: customerData } = useCustomerProfileDetails(FetchCustomerProfile)
  console.log(customerData?.data.basic_info.full_name, "customerData")

  const basicInformation = [
    {
      title: "Full Name:",
      info: customerData?.data.basic_info.full_name,
    },
    {
      title: "Email:",
      info: customerData?.data.basic_info.email,
    },
    {
      title: "Phone Number:",
      info: customerData?.data.basic_info.phone_number,
    },
    {
      title: "Gender:",
      info: customerData?.data.basic_info.gender,
    },
    {
      title: "Date of Birth:",
      info: "! 27.06.1990",
    },
    {
      title: "Address:",
      info: customerData?.data.basic_info.address,
    },
    {
      title: "KYC Status:",
      info: customerData?.data.basic_info.kyc_status,
    },
  ];


  const accountInformation = [
    {
      title: "Customer ID:",
      info: customerData?.data.account_info.customer_id,
    },
    {
      title: "Account Status:",
      info: customerData?.data.account_info.account_status,
    },
    {
      title: "KYC Status:",
      info: customerData?.data.basic_info.kyc_status,
    },
    {
      title: "Account Type:",
      info: customerData?.data.account_info.account_type,
    },
    {
      title: "Date Created:",
      info: customerData?.data.account_info.date_created,
    },
    {
      title: "Last Login:",
      info: customerData?.data.account_info.last_login_date,
    },

  ];


  // const financialInformation = [
  //   {
  //     title: "Total Transactions:",
  //     info: "₦" + customerData?.data.wallets.
  //   },
  //   {
  //     title: "Last Transaction:",
  //     info: "₦" + customerData?.data.financial_summary.last_transaction_amount + " - " + customerData?.data.financial_summary.last_transaction_type,
  //   },
  //   {
  //     title: "Wallet Balance:",
  //     info: "₦" + customerData?.data.financial_summary.current_wallet_balance,
  //   },
  //   {
  //     title: "Pending Bills:",
  //     info: "₦" + customerData?.data.financial_summary.pending_transactions_value,
  //   },
  //   {
  //     title: "Failed Transactions:",
  //     info: "₦" + customerData?.data.financial_summary.failed_transactions_value,
  //   },
  //   ];
  return (
    <div className=" h-[90vh] w-[280px] flex flex-col border-1 border-[#E9E9E9] rounded-[8px] overflow-auto no-scrollbar">
      {/* Basic Information */}
      <div className=" px-[11px] pt-[14px] ">
        <p className="text-[#4A4A68CC] text-[14px] font-medium  ">
          Basic Information
        </p>

        {basicInformation.map((basic, index) => (
          <div
            key={index}
            className="flex  items-center text-[#4A4A68] text-[12px] opacity-70 font-normal pt-2 pb-1 "
          >
            <p className=" opacity-70 basis-[50%] ">{basic.title} </p>
            <span className=" opacity-100 basis-[50%]">{basic.info}</span>
          </div>
        ))}
      </div>


      {/* Account Numbers */}
      <div className=" px-[11px] pt-[14px] ">
        <p className="text-[#4A4A68CC] text-[14px] font-medium border-t-[0.4px] border-[#4A4A6833] pt-3">
          Account Numbers
        </p>


        <div className="flex justify-between items-center text-[#4A4A68] text-[12px]  opacity-70 font-normal pt-2 pb-1">
          <p className=" opacity-70 basis-[50%]">VFD Bank:</p>
          <div className="flex gap-2 items-center basis-[50%]">
            <span className=" opacity-100">
              N/A
            </span>
            <Link href="#">
              <div className="bg-[#0322820A] border-[0.4px] border-[#032282] text-[#032282] rounded-xl text-[10px] py-1 px-2">Force Generate</div>
            </Link>
          </div>
        </div>

        <div className="flex justify-between items-center text-[#4A4A68] text-[12px]  opacity-70 font-normal pt-2 pb-1">
          <p className=" opacity-70 basis-[50%] ">Wema Bank:</p>
          <div className="flex gap-2 items-center basis-[50%]">
            <span className=" opacity-100">
              **********
            </span>
            <CopyLogo />
          </div>
        </div>

        <div className="flex justify-between items-center text-[#4A4A68] text-[12px]  opacity-70 font-normal pt-2 pb-1">
          <p className=" opacity-70 basis-[50%] ">Fidelity:</p>
          <div className="flex gap-2 basis-[50%] items-center">
            <span className=" opacity-100">
              **********
            </span>
            <CopyLogo />

          </div>
        </div>


      </div>


      {/* Business Numbers */}
      <div className=" px-[11px] pt-[14px]  ">
        <div className="flex gap-2 items-center justify-between border-t-[0.4px] border-[#4A4A6833] pt-3">
          <p className="text-[#4A4A68CC] basis-[50%] text-[14px] font-medium ">
            Business Numbers
          </p>
          {/* <Link href="#" className="basis-[50%] flex justify-start">
            <div className=" bg-[#0322820A] border-[0.4px] border-[#032282] text-[#032282] rounded-xl text-[10px] py-1 px-2">Create Account</div>
            </Link> */}

          <CreateAccountModal />
        </div>



        <div className="flex  items-center text-[#4A4A68] text-[12px]  opacity-70 font-normal pt-2 pb-1">
          <p className=" opacity-70 basis-[50%]">Account Name:</p>
          <div className="flex gap-2 basis-[50%] items-center">
            <span className=" opacity-100">
              N/A
            </span>


          </div>
        </div>

        <div className="flex  items-center text-[#4A4A68] text-[12px]  opacity-70 font-normal pt-2 pb-1">
          <p className=" opacity-70 basis-[50%]">Wema Bank:</p>
          <div className="flex gap-2 basis-[50%] items-center">
            <span className=" opacity-100">
              N/A
            </span>


          </div>
        </div>

        <div className="flex  items-center text-[#4A4A68] text-[12px]  opacity-70 font-normal pt-2 pb-1">
          <p className=" opacity-70  basis-[50%]">Fidelity:</p>
          <div className="flex gap-2 items-center justify-start basis-[50%]">
            <span className="flex opacity-100 justify-start">
              N/A
            </span>


          </div>
        </div>


      </div>

      {/* Account Information */}
      <div className=" px-[11px] pt-[14px] ">
        <p className="text-[#4A4A68CC] text-[14px] font-medium border-t-[0.4px] border-[#4A4A6833] pt-3 ">
          Account Information
        </p>

        {accountInformation.map((account, index) => (
          <div
            key={index}
            className="flex  items-center text-[#4A4A68] text-[12px] opacity-70 font-normal pt-2 pb-1 "
          >
            <p className=" opacity-70 basis-[50%] ">{account.title} </p>
            <span className=" opacity-100 basis-[50%]">{account.info}</span>
          </div>
        ))}
      </div>

      {/* Financial Summary */}
      {/* <div className=" px-[11px] pt-[14px] ">
        <p className="text-[#4A4A68CC] text-[14px] font-medium border-t-[0.4px] border-[#4A4A6833] pt-3 ">
          Financial Summary
        </p>

        {financialInformation.map((financial, index) => (
          <div
            key={index}
            className="flex  items-center text-[#4A4A68] text-[12px] opacity-70 font-normal pt-2 pb-1 "
          >
            <p className=" opacity-70 basis-[50%] ">{financial.title} </p>
            <span className=" opacity-100 basis-[50%]">{financial.info}</span>
          </div>
        ))}
      </div> */}
    </div>
  );
}
