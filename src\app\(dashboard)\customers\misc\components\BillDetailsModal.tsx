import { But<PERSON> } from "@/components/core";
import {
  Dialog,
  DialogContent,
  // DialogDescription,
  // DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

import { useBillsTransactionDetailsInfo } from "../api/BillsTransactionDetailsInfo";
// import { Input } from "@/components/ui/input";
// import { Label } from "@radix-ui/react-label";

interface FetchBillDetails{
  bill_id: string;
}



export function BillDetailsModal({bill_id}:FetchBillDetails) {
  console.log(bill_id, "jgvg")

  const FetchBillDetails  = {
      bill_id: bill_id,
    }


  const{data: billsInfoDataResponse} = useBillsTransactionDetailsInfo(FetchBillDetails)
    console.log(billsInfoDataResponse)
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button className="p-0 text-sm text-black font-normal">
          View Bills
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[452px] pb-5 pt-0 px-1  rounded-[20px] ">
        <DialogHeader>
          <DialogTitle className="flex items-center font-medium text-[16px] border-b-[0.8px] border-[#E9EBEE] pt-[14px] pb-[14px] px-[14px]">
            Bills Transaction Details
          </DialogTitle>
           
          {/* <DialogDescription className="flex   py-[12px] px-[24px] ">
           
            <div>
              <p className="text-[12px]  font-normal">Amount</p>
              <p className="text-[24px] font-semibold">₦10,000</p>
            </div>
            <div className="justify-end">
              <div className="flex items-center justify-end gap-1">
                <div className="size-1.5 bg-[#EEBE3E] rounded-full" />
                <p className=" text-[12px]  font-normal">Pending</p>
              </div>
              <p className="text-[12px] font-normal">
                Tuesday, 29th March, 2025
              </p>
            </div>
          </DialogDescription> */}

          {/* Amount */}
        </DialogHeader>
        <div className="max-h-[700px] overflow-auto no-scrollbar">
          <div className="flex items-center justify-between text-black border-b-[0.8px] border-[#E9EBEE] pb-[14px] px-[14px]">
          <div className="flex flex-col  ">
              <p className="text-[12px]  font-normal">Amount</p>
              <p className="text-[24px] font-semibold">{'₦' + billsInfoDataResponse?.amount}</p>
            </div>
            <div className="justify-end">
              <div className="flex items-center justify-end gap-1">
                <div className="size-1.5 bg-[#EEBE3E] rounded-full" />
                <p className=" text-[12px]  font-normal">{ billsInfoDataResponse?.status}</p>
              </div>
              <p className="text-[12px] font-normal">
                { billsInfoDataResponse?.date}
              </p>
            </div>
            </div>

            
        <div className="">

          {/* Overview */}
          <div className="flex flex-col py-[14px] px-[14px]">
            <p className="text-[16px] font-normal ">Overview</p>
            <div className="flex items-center justify-between pt-[12px]">
              <p className="text-[12px] font-normal text-[#000000B2]">
                Transaction ID
              </p>
              <p className="text-[12px] font-normal">{ billsInfoDataResponse?.id}</p>
            </div>
            <div className="flex items-center justify-between pt-[12px]">
              <p className="text-[12px] font-normal text-[#000000B2]">
                Reference Number
              </p>
              <p className="text-[12px] font-normal">R-33284JJD</p>
            </div>
            <div className="flex items-center justify-between pt-[12px]">
              <p className="text-[12px] font-normal text-[#000000B2]">
                Bill Type
              </p>
              <p className="text-[12px] font-normal">{ billsInfoDataResponse?.type}</p>
            </div>
            <div className="flex items-center justify-between pt-[12px]">
              <p className="text-[12px] font-normal text-[#000000B2]">Status</p>
              <p className="text-[12px] font-normal">{ billsInfoDataResponse?.status}</p>
            </div>
            <div className="flex items-center justify-between pt-[12px]">
              <p className="text-[12px] font-normal text-[#000000B2]">Amount</p>
              <p className="text-[12px] font-normal">{'₦' + billsInfoDataResponse?.amount}</p>
            </div>
            <div className="flex items-center justify-between pt-[12px]">
              <p className="text-[12px] font-normal text-[#000000B2]">
                Date & Time
              </p>
              <p className="text-[12px] font-normal">{ billsInfoDataResponse?.date}</p>
            </div>
            <div className="flex items-center justify-between pt-[12px]">
              <p className="text-[12px] font-normal text-[#000000B2]">
                Channel
              </p>
              <p className="text-[12px] font-normal">{ billsInfoDataResponse?.channel}</p>
            </div>
            <div className="flex items-center justify-between pt-[12px]">
              <p className="text-[12px] font-normal text-[#000000B2]">
                Biller
              </p>
              <p className="text-[12px] font-normal">{ billsInfoDataResponse?.biller}</p>
            </div>
          </div>


          {/* Bills Details */}
            {/* <div className="flex flex-col py-[14px] px-[14px] border-y-[0.8px] border-[#E9EBEE] ">
              <p className="text-[16px] font-normal">Bills Details</p>
              <div className="flex items-center justify-between pt-[12px]">
                <p className="text-[12px] font-normal text-[#000000B2]">
                  Meter Number
                </p>
                <p className="text-[12px] font-normal">**********</p>
              </div>
              <div className="flex items-center justify-between pt-[12px]">
                <p className="text-[12px] font-normal text-[#000000B2]">
                  Account Type
                </p>
                <p className="text-[12px] font-normal">Prepaid / Postpaid</p>
              </div>
              <div className="flex items-center justify-between pt-[12px]">
                <p className="text-[12px] font-normal text-[#000000B2]">
                  Tariff Plan
                </p>
                <p className="text-[12px] font-normal">R2 Residential</p>
              </div>
              <div className="flex items-center justify-between pt-[12px]">
                <p className="text-[12px] font-normal text-[#000000B2]">
                  Units Purchased
                </p>
                <p className="text-[12px] font-normal">107.4 kWh</p>
              </div>
              <div className="flex items-center justify-between pt-[12px]">
                <p className="text-[12px] font-normal text-[#000000B2]">Token</p>
                <p className="text-[12px] font-normal">7291-4758-2937-1830</p>
              </div>
              <div className="flex items-center justify-between pt-[12px]">
                <p className="text-[12px] font-normal text-[#000000B2]">
                  Customer Address
                </p>
                <p className="text-[12px] font-normal">24 Unity Street, Lagos</p>
              </div>
            </div> */}



          {/* Processing Timestamp */}
          <div className="flex flex-col py-[8px] px-[14px] border-t-[0.8px] border-[#E9EBEE] pt-[18px]">
            <p className="text-[16px] font-normal">Processing Timestamp</p>
            <div className="flex items-start justify-start gap-1 pt-[12px]">
              <div className="pt-1">
                <div className="size-2 bg-[#10A95E] rounded-full" />
              </div>

              <div className="flex flex-col items-start gap-1">
                <p className="text-[12px]  font-normal">
                  Bill Payment Initiated
                </p>
                <p className="text-[12px]  font-normal text-[#242424]">
                  9/5/2025 09:42am
                </p>
              </div>
            </div>
            <div className="flex items-start justify-start gap-1 pt-[12px] ">
              <div className="pt-1">
                <div className="size-2  bg-[#10A95E] rounded-full " />
              </div>

              <div className="flex flex-col items-start gap-1 ">
                <p className="text-[12px]  font-normal">Payment Confirmed</p>
                <p className="text-[12px]  font-normal text-[#242424]">
                  9/5/2025 09:42am
                </p>
              </div>
            </div>

            <div className="flex items-start justify-start gap-1 pt-[12px] ">
              <div className="pt-1">
                <div className="size-2  bg-[#F7D66D] rounded-full " />
              </div>

              <div className="flex flex-col items-start gap-1 ">
                <p className="text-[12px]  font-normal">
                  Request Sent to Biller
                </p>
                <p className="text-[12px]  font-normal text-[#242424]">
                  9/5/2025 09:42am
                </p>
              </div>
            </div>

            <div className="flex items-start justify-start gap-1 pt-[12px] ">
              <div className="pt-1">
                <div className="size-2  bg-[#E9EBEE] rounded-full " />
              </div>

              <div className="flex flex-col items-start gap-1 ">
                <p className="text-[12px]  font-normal">
                  Biller Response: Success
                </p>
                <p className="text-[12px]  font-normal text-[#242424]">
                  9/5/2025 09:42am
                </p>
              </div>
            </div>

            <div className="flex items-start justify-start gap-1 pt-[12px] ">
              <div className="pt-1">
                <div className="size-2  bg-[#E9EBEE] rounded-full " />
              </div>

              <div className="flex flex-col items-start gap-1 ">
                <p className="text-[12px]  font-normal">
                  Token Delivered (if electricity)
                </p>
                <p className="text-[12px]  font-normal text-[#242424]">
                  9/5/2025 09:42am
                </p>
              </div>
            </div>

           
          </div>
        </div>
        </div>
        {/* <DialogFooter>
          <Button type="submit">Save changes</Button>
        </DialogFooter> */}
      </DialogContent>
    </Dialog>
  );
}
