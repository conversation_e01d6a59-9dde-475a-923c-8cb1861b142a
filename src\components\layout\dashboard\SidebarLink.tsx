'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import * as React from 'react';

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/core';
import { cn } from '@/utils/classNames';

interface SidebarLinkProps {
  icon: React.JSX.Element;
  link: string;
  text: string;
  comingSoon?: boolean;
}

export function SidebarLink({ icon, link, text, comingSoon }: SidebarLinkProps) {
  const pathname = usePathname();
  const isSelected = pathname === link;

  console.log("pathname", pathname);
  console.log("link", link);

  return (
    <Link
      className={cn(
        'flex grow items-center justify-between gap-1 rounded-lg p-3 text-sm transition duration-500 ease-in-out hover:bg-[#1D2739]  md:py-2',
        isSelected && 'bg-[#1D2739]'
      )}
      href={link}
      rel={link.startsWith('https://') ? 'noopener noreferrer' : undefined}
      target={link.startsWith('https://') ? '_blank' : undefined}
    >
      <span className="flex items-center gap-3">
        <span className="h-5 w-5 shrink-0">
          {{ ...icon, props: { isSelected } }}
        </span>
        <span>{text}</span>
        {comingSoon && (
          <span className="max-w-[30px] truncate rounded-[10px] bg-white px-[8px]  text-[12px] font-medium text-[#344054]">
            10
          </span>
        )}
      </span>
    </ Link>
  );
}

interface SidebarCollapsibleWithLinkProps {
  icon: React.JSX.Element;
  link: string;
  comingSoon?: boolean;
  text: string;
  nestedLinks: {
    link: string;
    text: string;
    disabled?: boolean;
    comingSoon?: boolean;
  }[];
}

export function SidebarCollapsibleWithLink({
  icon,
  link,
  text,
  nestedLinks,
  comingSoon
}: SidebarCollapsibleWithLinkProps) {
  const pathname = usePathname();
  const isSelected = pathname === link;

  return (
    <Collapsible>
      <div className="mb-1 flex items-center justify-between gap-6 text-sm md:gap-px">
        <Link
          className={cn(
            'grow items-center justify-between gap-1 rounded-lg px-3 py-3 text-sm transition duration-500 ease-in-out hover:bg-sidebar-link-active hover:bg-opacity-60 md:py-2',
            isSelected && 'bg-sidebar-link-active'
          )}
          href={link}
          rel={link.startsWith('https://') ? 'noopener noreferrer' : undefined}
          target={link.startsWith('https://') ? '_blank' : undefined}
        >
          <span className="flex items-center gap-3">
            <span className="h-5 w-5 shrink-0">
              {{ ...icon, props: { isSelected } }}
            </span>
            <span>{text}</span>
            {comingSoon && (
              <span className="max-w-[30px] truncate rounded-[10px] bg-[#ffffff] px-[8px]  text-[12px] font-medium text-[#344054]">
                10
              </span>
            )}
          </span>
        </Link>

        <CollapsibleTrigger className="mr-1 inline-block h-7 w-7 shrink-0 rounded-md transition duration-300 ease-in-out data-[state=closed]:rotate-0 data-[state=open]:rotate-90">
          <span>
            <svg
              fill="none"
              height={25}
              viewBox="0 0 28 25"
              width={28}
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clipRule="evenodd"
                d="M16.054 15.636c1.649-1.53 1.674-3.948.097-5.5l-.169-.156-4.6-3.821a1.188 1.188 0 0 0-1.561.02.931.931 0 0 0-.076 1.32l.093.094 4.6 3.82c.827.73.884 1.905.163 2.7l-.128.129-4.501 3.935a.934.934 0 0 0 .018 1.415c.402.355 1.03.375 1.459.064l.103-.084 4.502-3.936Z"
                fill="#fff"
                fillRule="evenodd"
              />
            </svg>
          </span>
        </CollapsibleTrigger>
      </div>

      <CollapsibleContent>
        <ul className="space-y-1 animate-in slide-in-from-top-2">
          {nestedLinks.map(({ link, text, disabled, comingSoon }) => {
            const isSelected = pathname === link;

            return disabled ? (
              <button
                className={cn(
                  'block rounded-md py-2.5 pl-11 pr-3 text-sm transition duration-500 ease-in-out hover:bg-opacity-60 disabled:cursor-not-allowed disabled:opacity-60 md:py-1.5',
                  isSelected && 'bg-sidebar-link-active'
                )}
                key={link}
                disabled
              >
                <span>{text}</span>
                {comingSoon && (
                  <span className="max-w-[80px] truncate rounded-full bg-[#192749B2] px-[10px] py-1 text-[9px] text-[#5879FD]">
                    Coming Soon
                  </span>
                )}
              </button>
            ) : (
              <Link
                className={cn(
                  'block rounded-md py-2.5 pl-11 pr-3 text-sm transition duration-500 ease-in-out hover:bg-sidebar-link-active hover:bg-opacity-60 md:py-1.5',
                  isSelected && 'bg-sidebar-link-active'
                )}
                href={link}
                key={link}
                rel={link.startsWith('https://') ? 'noopener noreferrer' : undefined}
                target={link.startsWith('https://') ? '_blank' : undefined}
              >
                <span>{text}</span>
                {comingSoon && (
                  <span className="max-w-[80px] truncate rounded-full bg-[#192749B2] px-[10px] py-1 text-[9px] text-[#5879FD]">
                    Coming Soon
                  </span>
                )}
              </Link>
            );
          })}
        </ul>
      </CollapsibleContent>
    </Collapsible>
  );
}

interface SidebarCollapsibleProps {
  icon?: React.JSX.Element;
  text: string;
  nestedLinks: {
    link: string;
    text: string;
    disabled?: boolean;
    nestedLinks?: {
      link: string;
      text: string;
      disabled?: boolean;
    }[];
  }[];
}

export function SidebarCollapsible({
  icon,
  text,
  nestedLinks,

}: SidebarCollapsibleProps) {
  const pathname = usePathname();
  const [open, setOpen] = React.useState(false);

  return (
    <Collapsible open={open} onOpenChange={setOpen}>
      <div className="mb-1 flex items-center justify-between gap-6 text-sm md:gap-px">
        <CollapsibleTrigger
          className={cn(
            'flex grow items-center justify-between gap-1 rounded-lg px-3 py-2 pr-1 text-sm transition duration-500 ease-in-out hover:bg-sidebar-link-active data-[state=open]:bg-white/5 md:py-1.5'
          )}
        >
          <span className="flex items-center gap-3">
            {icon ? (
              <span className="h-5 w-5 shrink-0">
                {{ ...icon, props: { isSelected: open } }}
              </span>
            ) : (
              <span className="h-5 w-5 shrink-0"></span>
            )}

            <span className="text-left">{text}</span>
          </span>

          <span
            className={cn(
              'inline-flex shrink-0 items-center justify-center rounded-md transition duration-300 ease-in-out',
              open && 'rotate-90'
            )}
          >
            <svg
              fill="none"
              height={25}
              viewBox="0 0 28 25"
              width={28}
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clipRule="evenodd"
                d="M16.054 15.636c1.649-1.53 1.674-3.948.097-5.5l-.169-.156-4.6-3.821a1.188 1.188 0 0 0-1.561.02.931.931 0 0 0-.076 1.32l.093.094 4.6 3.82c.827.73.884 1.905.163 2.7l-.128.129-4.501 3.935a.934.934 0 0 0 .018 1.415c.402.355 1.03.375 1.459.064l.103-.084 4.502-3.936Z"
                fill="#fff"
                fillRule="evenodd"
              />
            </svg>
          </span>
        </CollapsibleTrigger>
      </div>

      <CollapsibleContent>
        <ul className="space-y-1 animate-in slide-in-from-top-2">
          {nestedLinks.map(({ link, text, disabled, nestedLinks }) => {
            const isSelected = pathname === link.replace(/\/(select-branch|select-company)\/?$/, '');

            if (nestedLinks) {
              return (
                <li key={link}>
                  <SidebarCollapsible
                    // icon={icon}
                    nestedLinks={nestedLinks}
                    text={text}
                  />
                </li>
              );
            }

            return disabled ? (
              <button
                className={cn(
                  'block rounded-md py-2.5 pl-11 pr-3 text-sm transition duration-500 ease-in-out hover:bg-opacity-60 disabled:cursor-not-allowed disabled:opacity-60 md:py-1.5',
                  isSelected && 'bg-sidebar-link-active',
                  !icon && 'pl-14'
                )}
                key={link}
                disabled
              >
                <span>{text}</span>
              </button>
            ) : (
              <Link
                className={cn(
                  'block rounded-md py-2.5 pl-11 pr-3 text-sm transition duration-500 ease-in-out hover:bg-sidebar-link-active hover:bg-opacity-60 md:py-1.5',
                  isSelected && 'bg-sidebar-link-active',
                  !icon && 'pl-14'
                )}
                href={link}
                key={link}
                rel={link.startsWith('https://') ? 'noopener noreferrer' : undefined}
                target={link.startsWith('https://') ? '_blank' : undefined}
              >
                <span>{text}</span>
              </Link>
            );
          })}
        </ul>
      </CollapsibleContent>
    </Collapsible>
  );
}
