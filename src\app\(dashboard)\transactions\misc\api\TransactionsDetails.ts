import { adminAxios } from "@/lib/axios";
import { useQuery } from "@tanstack/react-query";

export interface TransactionsData {
  count: number
  next: string
  previous: string
  results: Result[]
}

export interface Result {
  transaction_id: string
  type_of_transaction: string
  transaction_category: TransactionCategory
  amount_of_transaction: number
  transaction_status: string
  transaction_channel?: string
  user_name: string
  user_id: string
  transaction_date: string
  date_created: string
  date_modified: string
  transaction_reference: string
  user_wallet_type: string
}

export interface TransactionCategory {
  category: string
  type: string
  display: string
}

export const getTransactionsDetails = async () => {
    const response = await adminAxios.get(
        "/customer_care_management_system/transactions"
    );
    return response.data as TransactionsData;
};

export const useTransactionsDetails = () => {
    return useQuery({
        queryKey: ['transactions-details'], 
        queryFn: () => getTransactionsDetails(),
    });
}
