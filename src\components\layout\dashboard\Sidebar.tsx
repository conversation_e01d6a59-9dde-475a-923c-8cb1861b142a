"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { ArrowRightFromLine } from "lucide-react";
import { JSX, useState } from "react";

import { cn } from "@/utils/classNames";
// import Image from "next/image";

import {
  SidebarCollapsible,
  SidebarCollapsibleWithLink,
  SidebarLink,
} from "./SidebarLink";
import { useSidebarContext } from "./SidebarContext";

import DashboardLogo from "@/app/icons/sidebar/DashboardLogo";
import CustomerLogo from "@/app/icons/sidebar/CustomerLogo";
import TransactionLogo from "@/app/icons/sidebar/TransactionLogo";
import SupportLogo from "@/app/icons/sidebar/SupportLogo";
import MessageLogo from "@/app/icons/sidebar/MessageLogo";
import AnalyticsLogo from "@/app/icons/sidebar/AnalyticsLogo";
import SettingLogo from "@/app/icons/sidebar/SettingLogo";
import KnowledgeLogo from "@/app/icons/sidebar/KnowledgeLogo";
import { useUserDetails } from "@/app/(dashboard)/dashboard/misc/api/UserDetails";
import { convertKebabAndSnakeToTitleCase } from "@/utils/strings";
import { useUser } from "@/app/Login/api/getAuthenticatedUser";
// import { tokenStorage } from '@/app/(auth)/(onboarding)/misc';

// const {
//   user: { access },
// } = await getServerSessionOrRedirect({
//   authOptions,
// });

// const access = tokenStorage.getToken();

export const linkGroups = [
  {
    key: "top",
    heading: "",
    links: [
      {
        link: "/dashboard",
        text: "Dashboard",
        icon: <DashboardLogo activeState= {true} />,
        comingSoon: false,
      },
      {
        link: "/customers",
        text: "Customers",
        icon: <CustomerLogo />,
        comingSoon: false,
      },
      {
        link: "/transactions",
        text: "Transactions",
        icon: <TransactionLogo />,
        comingSoon: false,
      },
      {
        link: "/analytics/select-company",
        text: "Support Tickets",
        icon: <SupportLogo />,
        comingSoon: true,
      },
      {
        link: "/analytics/select-company",
        text: "Messages",
        icon: <MessageLogo />,
        comingSoon: false,
      },
      {
        link: "/analytics/select-company",
        text: "Analytics Report",
        icon: <AnalyticsLogo />,
        comingSoon: false,
      },
    ],
  },

  // {
  //   key: 'others',
  //   heading: '',
  //   links: [
  //     {
  //       link: '/leaderboard',
  //       text: 'Settings',
  //       icon: <SettingLogo />,
  //       comingSoon: false,
  //     },
  //     {
  //       link: '/profile-settings/personal-information',
  //       text: 'Knowledge Base',
  //       icon: <KnowledgeLogo />,
  //       comingSoon: false,
  //     },

  // {
  //   link: `https://liberty-paybox-dashboard-crm.vercel.app?user=${access}`,
  //   text: 'CRM',
  //   icon: <Shop />,
  // },
  //   ],
  // },
];

interface IconOnlyLinkProps {
  icon: JSX.Element;
  link: string;
  text: string;
  comingSoon?: boolean;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
function IconOnlyLink({ icon, link, text, comingSoon }: IconOnlyLinkProps) {
  const pathname = usePathname();
  
  console.log("pathname", pathname);
  console.log("link", link);

  const isSelected = pathname === link;

  return (
    <Link
      className={cn(
        "flex items-center justify-center rounded-lg p-2 transition duration-500 ease-in-out hover:bg-sidebar-link-active hover:bg-opacity-60",
        isSelected && "bg-sidebar-link-active"
      )}
      href={link}
      rel={link.startsWith("https://") ? "noopener noreferrer" : undefined}
      target={link.startsWith("https://") ? "_blank" : undefined}
      title={text}
    >
      <span className="h-5 w-5 shrink-0">
        {{ ...icon, props: { isSelected } }}
      </span>
      <span className="sr-only">{text}</span>
    </Link>
  );
}

interface CollapsedSubmenuProps {
  icon: JSX.Element;
  text: string;
  nestedLinks?: Array<{
    link: string;
    text: string;
    comingSoon?: boolean;
    disabled?: boolean;
  }>;
}

function CollapsedSubmenu({ icon, text, nestedLinks }: CollapsedSubmenuProps) {
  const [menuPosition, setMenuPosition] = useState({ top: 0, maxHeight: 0 });

  const updatePosition = (element: HTMLElement) => {
    const rect = element.getBoundingClientRect();
    const windowHeight = window.innerHeight;
    const spaceBelow = windowHeight - rect.top;
    const maxHeight = Math.min(spaceBelow - 20, 400); // Maximum height of 500px or space available

    setMenuPosition({
      top: rect.top,
      maxHeight: maxHeight,
    });
  };

  return (
    <div
      className="group relative flex items-center justify-center rounded-lg p-2 transition duration-500 ease-in-out hover:bg-sidebar-link-active hover:bg-opacity-60"
      title={text}
      onMouseEnter={(e) => updatePosition(e.currentTarget)}
    >
      <span className="h-5 w-5 shrink-0 opacity-100">{icon}</span>
      <span className="sr-only">{text}</span>

      <div
        className={cn(
          "fixed left-[70px] z-[9999] min-w-[200px] rounded-lg bg-dash-dark-bg py-2 shadow-lg",
          "invisible opacity-0 transition-all duration-200 ease-in-out",
          "group-hover:visible group-hover:opacity-100",
          "overflow-y-auto"
        )}
        style={{
          top: `${menuPosition.top}px`,
          maxHeight: `${menuPosition.maxHeight}px`,
        }}
      >
        {nestedLinks?.map((nestedLink) => (
          <Link
            className="block px-4 py-2 text-sm text-white transition-colors hover:bg-sidebar-link-active hover:bg-opacity-60 whitespace-nowrap"
            href={nestedLink.link}
            key={nestedLink.link || nestedLink.text}
          >
            {nestedLink.text}
          </Link>
        ))}
      </div>
    </div>
  );
}

export function Sidebar() {
  const { data: user, isLoading } = useUser();
  const { collapsed, toggleSidebar } = useSidebarContext();

  const { data: userDetailsResponse } = useUserDetails();

  const { first_name } = userDetailsResponse?.user_data ?? {};

  const userData = user?.user_data;

  return (
    <div
      className={cn(
        "!no-scrollbar relative flex h-full flex-col",
        collapsed ? "w-16" : "w-[272px]",
        "transition-all duration-300 ease-in-out group-hover:ml-0"
      )}
    >
      {/* Header */}
      <div className="sticky top-0 z-10 flex items-center justify-between bg-dash-dark-bg px-3 py-6">
        {/* <Link
          className={cn(
            "flex items-center rounded-lg transition duration-500 ease-in-out hover:bg-[#192749]/50",
            collapsed ? "justify-center px-2" : "px-3",
          )}
          href="/dashboard"
        >
          <span className="sr-only">Go to dashboard</span>
          {!collapsed ? (
            <svg
              aria-label="The Paybox by LibertyPay logo"
              fill="none"
              height={45}
              viewBox="0 0 191 45"
              width={191}
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M6.976 22H0.96V0.559999H12.832C17.92 0.559999 21.12 3.472 21.12 8.56C21.12 13.648 17.92 16.592 12.832 16.592H6.976V22ZM12.16 5.968H6.976V11.184H12.16C14.272 11.184 15.136 10.768 15.136 8.56C15.136 6.384 14.272 5.968 12.16 5.968ZM27.6135 22.32C23.9655 22.32 21.9175 20.656 21.9175 17.904C21.9175 15.632 23.4855 14 27.1335 13.648L33.6935 13.008V12.688C33.6935 11.056 32.9895 10.8 30.8455 10.8C28.8615 10.8 28.2535 11.184 28.2535 12.528V12.656H22.2375V12.592C22.2375 8.304 25.8215 5.552 31.2935 5.552C36.9255 5.552 39.6455 8.304 39.6455 12.816V22H34.0135V18.608H33.6935C33.0855 20.88 31.1015 22.32 27.6135 22.32ZM27.9655 17.52C27.9655 18.032 28.4775 18.128 29.4055 18.128C32.3175 18.128 33.5015 17.776 33.6615 16.336L28.7335 16.912C28.1895 16.976 27.9655 17.168 27.9655 17.52ZM44.9808 27.44H42.0688V22H46.7408C47.2208 22 47.6048 21.936 47.8608 21.808L40.1487 5.872H46.9968L49.7808 12.208L50.9328 15.92H51.3488L52.4048 12.144L54.8048 5.872H61.5248L53.8768 22.64C52.1488 26.448 49.6848 27.44 44.9808 27.44ZM67.8108 22H62.1788V0.559999H68.1948V10.032H68.5148C68.9948 7.344 70.7868 5.552 74.5948 5.552C79.3308 5.552 81.9868 8.688 81.9868 13.936C81.9868 19.152 79.2668 22.32 74.3388 22.32C70.5628 22.32 68.6428 20.848 68.1308 17.648H67.8108V22ZM68.1948 13.968C68.1948 16.304 69.4748 16.816 72.1628 16.816C74.9468 16.816 75.9068 16.08 75.9068 13.936C75.9068 11.792 74.9468 11.024 72.1628 11.024C69.4748 11.024 68.1948 11.472 68.1948 13.744V13.968ZM92.513 22.32C86.913 22.32 83.105 19.152 83.105 13.936C83.105 8.688 86.913 5.552 92.513 5.552C98.113 5.552 101.921 8.688 101.921 13.936C101.921 19.152 98.113 22.32 92.513 22.32ZM92.513 17.008C95.233 17.008 95.969 16.272 95.969 13.936C95.969 11.6 95.233 10.832 92.513 10.832C89.793 10.832 89.057 11.6 89.057 13.936C89.057 16.272 89.793 17.008 92.513 17.008ZM109.35 22H102.086L108.102 14.192V13.872L102.086 5.872H109.446L112.742 10.736H113.062L116.23 5.872H123.494L117.478 13.776V14.096L123.494 22H116.134L112.838 17.264H112.518L109.35 22ZM134.452 22.32C127.22 22.32 123.828 19.6 123.828 14.512V14H130.228V14.576C130.228 16.176 130.356 16.56 134.164 16.56C137.812 16.56 138.772 16.24 138.772 14.928C138.772 13.872 138.164 13.52 136.756 13.52H130.388V8.592H136.308C137.492 8.592 137.876 8.176 137.876 7.376C137.876 6.352 137.3 6 134.1 6C130.804 6 130.228 6.384 130.228 7.984V8.08H123.828V8.048C123.828 3.28 127.38 0.24 134.676 0.24C140.628 0.24 143.86 2.128 143.86 6C143.86 8.496 142.356 10.096 139.508 10.416V10.832C142.708 11.184 144.884 12.688 144.884 15.952C144.884 20.176 141.044 22.32 134.452 22.32ZM157.379 22.32C149.091 22.32 145.795 18.832 145.795 11.44C145.795 3.472 150.531 0.24 156.771 0.24C163.107 0.24 166.947 3.184 166.947 7.6V7.888H160.227V7.6C160.227 6.128 159.203 6 156.771 6C153.571 6 152.259 6.48 152.259 10.224V11.376H152.675C153.635 9.968 155.651 8.752 159.267 8.752C164.579 8.752 167.459 10.704 167.459 15.184C167.459 19.536 164.035 22.32 157.379 22.32ZM157.027 16.528C160.451 16.528 161.123 15.984 161.123 14.736C161.123 13.328 160.451 12.848 157.027 12.848C153.187 12.848 152.611 13.328 152.611 14.736C152.611 15.984 153.283 16.528 157.027 16.528ZM179.932 22.32C172.636 22.32 168.828 17.872 168.828 11.28C168.828 4.72 172.636 0.24 179.932 0.24C187.196 0.24 191.004 4.72 191.004 11.28C191.004 17.872 187.196 22.32 179.932 22.32ZM179.932 16.464C183.58 16.464 184.54 15.248 184.54 11.28C184.54 7.312 183.58 6.096 179.932 6.096C176.252 6.096 175.292 7.312 175.292 11.28C175.292 15.248 176.252 16.464 179.932 16.464Z"
                fill="white"
              />
              <path
                d="M1.488 42H0.72V33.96H1.572V37.728H1.632C1.884 36.708 2.736 35.952 4.164 35.952C6.024 35.952 7.044 37.236 7.044 39.036C7.044 40.836 6.024 42.12 4.092 42.12C2.772 42.12 1.824 41.436 1.548 40.248H1.488V42ZM1.572 39.132C1.572 40.536 2.46 41.34 3.888 41.34C5.304 41.34 6.18 40.752 6.18 39.036C6.18 37.32 5.28 36.744 3.912 36.744C2.412 36.744 1.572 37.56 1.572 39.024V39.132ZM8.87109 44.04H8.12709V43.26H9.02709C9.62709 43.26 9.85509 43.08 10.0831 42.588L10.3711 41.988L7.43109 36.072H8.36709L10.1071 39.6L10.7671 41.052H10.8391L11.4751 39.588L13.1191 36.072H14.0551L10.8391 42.828C10.4071 43.74 9.85509 44.04 8.87109 44.04ZM23.0314 42H17.0794V33.96H17.9314V41.22H23.0314V42ZM24.8454 35.316H23.9934V33.96H24.8454V35.316ZM24.8454 42H23.9934V36.072H24.8454V42ZM27.0466 42H26.2786V33.96H27.1306V37.728H27.1906C27.4426 36.708 28.2946 35.952 29.7226 35.952C31.5826 35.952 32.6026 37.236 32.6026 39.036C32.6026 40.836 31.5826 42.12 29.6506 42.12C28.3306 42.12 27.3826 41.436 27.1066 40.248H27.0466V42ZM27.1306 39.132C27.1306 40.536 28.0186 41.34 29.4466 41.34C30.8626 41.34 31.7386 40.752 31.7386 39.036C31.7386 37.32 30.8386 36.744 29.4706 36.744C27.9706 36.744 27.1306 37.56 27.1306 39.024V39.132ZM36.4697 42.12C34.5617 42.12 33.3497 40.92 33.3497 39.036C33.3497 37.236 34.5497 35.952 36.4577 35.952C38.1977 35.952 39.4337 36.96 39.4337 38.724C39.4337 38.94 39.4097 39.12 39.3737 39.276H34.1537C34.2017 40.608 34.8857 41.412 36.4577 41.412C37.8497 41.412 38.4977 40.896 38.4977 40.032V39.948H39.3497V40.032C39.3497 41.268 38.1257 42.12 36.4697 42.12ZM36.4457 36.66C34.9097 36.66 34.2137 37.452 34.1537 38.748H38.6297C38.6297 38.688 38.6297 38.628 38.6297 38.568C38.6297 37.32 37.8377 36.66 36.4457 36.66ZM41.4158 42H40.5638V36.072H41.3318V37.692H41.3918C41.5718 36.744 42.2438 35.952 43.4798 35.952C44.8478 35.952 45.4478 36.96 45.4478 38.064V38.652H44.5958V38.196C44.5958 37.188 44.1758 36.696 43.1438 36.696C41.9438 36.696 41.4158 37.452 41.4158 38.784V42ZM50.0708 42H48.7868C47.6108 42 46.8548 41.508 46.8548 40.116V36.804H45.7988V36.072H46.8548V34.656H47.7188V36.072H50.0708V36.804H47.7188V40.164C47.7188 40.992 48.1268 41.22 48.9908 41.22H50.0708V42ZM51.7852 44.04H51.0412V43.26H51.9412C52.5412 43.26 52.7692 43.08 52.9972 42.588L53.2852 41.988L50.3452 36.072H51.2812L53.0212 39.6L53.6812 41.052H53.7532L54.3892 39.588L56.0332 36.072H56.9692L53.7532 42.828C53.3212 43.74 52.7692 44.04 51.7852 44.04ZM58.6423 42H57.7903V33.96H61.4863C63.2263 33.96 64.4503 34.956 64.4503 36.672C64.4503 38.4 63.2263 39.396 61.4863 39.396H58.6423V42ZM61.4143 34.74H58.6423V38.616H61.4143C62.8303 38.616 63.5863 38.04 63.5863 36.672C63.5863 35.328 62.8303 34.74 61.4143 34.74ZM67.0163 42.12C65.8523 42.12 65.0603 41.568 65.0603 40.608C65.0603 39.636 65.8643 39.216 66.9683 39.096L69.7883 38.784V38.328C69.7883 37.176 69.2843 36.72 67.9883 36.72C66.7163 36.72 66.0443 37.176 66.0443 38.22V38.268H65.1923V38.22C65.1923 36.972 66.2243 35.952 68.0483 35.952C69.8483 35.952 70.6163 36.984 70.6163 38.292V42H69.8483V40.404H69.7883C69.4403 41.496 68.3963 42.12 67.0163 42.12ZM65.9123 40.548C65.9123 41.148 66.3083 41.46 67.2083 41.46C68.6483 41.46 69.7883 40.824 69.7883 39.396V39.348L67.2323 39.636C66.3443 39.72 65.9123 39.936 65.9123 40.548ZM72.7969 44.04H72.0529V43.26H72.9529C73.5529 43.26 73.7809 43.08 74.0089 42.588L74.2969 41.988L71.3569 36.072H72.2929L74.0329 39.6L74.6929 41.052H74.7649L75.4009 39.588L77.0449 36.072H77.9809L74.7649 42.828C74.3329 43.74 73.7809 44.04 72.7969 44.04Z"
                fill="white"
              />
            </svg>
          ) : (
            <div className="flex aspect-square size-10 items-center justify-center rounded-lg bg-[#2D4696] text-white">

              <svg
                height={24}
                style={{
                  shapeRendering: "geometricPrecision",
                  textRendering: "geometricPrecision",
                  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                  //@ts-ignore
                  imageRendering: "optimizeQuality",
                  fillRule: "evenodd",
                  clipRule: "evenodd",
                }}
                viewBox="0 0 210 235"
                width={24}
                xmlns="http://www.w3.org/2000/svg"
                xmlnsXlink="http://www.w3.org/1999/xlink"
                xmlSpace="preserve"

              >
                <defs>
                  <style type="text/css" />
                </defs>
                <g id="Layer_x0020_1">
                  <metadata id="CorelCorpID_0Corel-Layer" />
                  <path
                    className="fil0"
                    d="M115 235l47 -29 48 -30 0 -58 0 -59 -48 -30 -47 -29 -48 29 -47 30 0 59 0 58 47 30 48 29zm4 -13l82 -50 0 -105 -82 50 0 105zm-8 -1l-82 -49 0 -105 82 50 0 104zm4 -111l83 -50 -83 -51 -83 52 83 49z"
                  />
                  <polygon className="fil1" points="129,202 190,165 191,87 129,124 " />
                  <polygon className="fil0" points="100,201 40,165 39,87 101,124 " />
                  <polygon className="fil0" points="115,97 176,60 115,22 53,61 " />
                  <g>
                    <polyline className="fil2 str0" points="57,19 5,51 5,115 5,171 " />
                  </g>
                </g>
              </svg>
            </div>
          )}
        </Link> */}

        <div className="flex">
          <div className="flex px-3">
            {!isLoading && (
              <button className="rounded-full w-9 h-9 text-sm text-white flex justify-center items-center shrink-0 bg-[#FFFFFF4D] ">
                {userData?.profile_picture === null ? (
                  <>
                    {`${userData?.first_name?.slice(0, 1) ?? ""}${
                      userData?.last_name?.slice(0, 1) ?? ""
                    }`}
                  </>
                ) : (
                  <>{userData?.profile_picture}</>
                )}
              </button>
            )}
            {/* <Image
              src="/images/Avatars.png"
              alt="Avatar"
              height={40}
              width={40}
              className="object-cover rounded-lg md:w-[40px] md:h-[40px] "
            /> */}
          </div>
          <div className="">
            <p className="text-[#FFFFFF] font-semibold text-[14px]">
              {/* Chris Aniedi */}
              {userData?.first_name} {userData?.last_name}
              {convertKebabAndSnakeToTitleCase(first_name)}
            </p>
            <p className="text-[#E3EFFC] font-normal text-[12px]">
              {/* <EMAIL> */}
              {userData?.email}
            </p>
          </div>
        </div>

        <button
          aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
          className="absolute left-full top-20 z-50 flex h-10 w-2 items-center justify-center rounded-e-md bg-dash-dark-bg text-[#192749] shadow-[0_2px_12px_rgba(0,0,0,0.1)] transition-all duration-300 ease-in-out hover:w-10 hover:bg-dash-dark-bg group"
          onClick={toggleSidebar}
        >
          <ArrowRightFromLine
            className="transition-all duration-300 text-white opacity-0 group-hover:opacity-100 -ml-8 group-hover:ml-0"
            size={15}
            style={{ transform: collapsed ? "rotate(0deg)" : "rotate(180deg)" }}
          />
        </button>
      </div>

      <nav className="relative flex-1 pl-2 pr-3 ">
        <ul className="overflow-y-auto  ">
          {linkGroups.map(({ heading, key, links }) => {
            return (
              <li className="py-20 first-of-type:pt-2" key={key}>
                {!collapsed && heading && (
                  <h2 className="mb-1 px-3 uppercase text-xs opacity-70">
                    {heading}
                  </h2>
                )}

                <ul className="space-y-1 opacity-80">
                  {links.map(
                    ({ 
                       // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                      //@ts-ignore
                      icon, link, text, nestedLinks, comingSoon }) => {
                      return (
                        <li key={link || text}>
                          {!!nestedLinks && !!link && !collapsed && (
                            <SidebarCollapsibleWithLink
                              icon={icon}
                              link={link}
                              nestedLinks={nestedLinks}
                              text={text}
                            />
                          )}

                          {!!nestedLinks && !link && !collapsed && (
                            <SidebarCollapsible
                              icon={icon}
                              nestedLinks={nestedLinks}
                              text={text}
                            />
                          )}

                          {!nestedLinks &&
                            !!link &&
                            (collapsed ? (
                              <IconOnlyLink
                                comingSoon={comingSoon}
                                icon={icon}
                                link={link}
                                text={text}
                              />
                            ) : (
                              <SidebarLink
                                comingSoon={comingSoon}
                                icon={icon}
                                link={link}
                                text={text}
                              />
                            ))}

                          {!!nestedLinks && collapsed && (
                            <CollapsedSubmenu
                              icon={icon}
                              nestedLinks={nestedLinks}
                              text={text}
                            />
                          )}
                        </li>
                      );
                    }
                  )}
                </ul>
              </li>
            );
          })}
        </ul>
      </nav>
      <div>
        <div className="absolute bottom-0 left-0 flex flex-col w-full   px-6">
          <div className="border-b-2 border-[#F0F2F599] ">
            <Link href={"#"}>
              <div className="flex items-center h-[44px]  ">
                <SettingLogo />
                <p className="text-white opacity-80 text-[14px] font-normal px-3 ">
                  Settings
                </p>
              </div>
            </Link>
            <Link href={"#"}>
              <div className="flex items-center h-[44px] ">
                <KnowledgeLogo />
                <p className="text-white opacity-80 text-[14px] font-normal px-3 ">
                  Knowledge Base
                </p>
              </div>
            </Link>
          </div>
        </div>
        <div></div>
      </div>
      {/* <div className="flex items-center justify-center font-bold text-lg text-center py-4  w-full   h-16">
            <Image
              src="/images/libertyPayWhite.png"
              alt="Liberty Pay"
              height={28}
              width={120}
              className=" md:w-[152px] md:h-[33px]"
            />
          </div> */}
    </div>
  );
}
