"use client"
import { Search } from "lucide-react"

import { <PERSON><PERSON>utton } from "@/components/core"
// import { useSidebarContext } from "./sidebar-context"
import { DashboardHeaderProfileMenu } from "./DasbboardHeaderProfileMenu"
// import { DashboardHeaderHeading } from "./DashboardHeaderHeading"
import { DashboardHeaderHeadingAcccessories } from "./DashboardHeaderHeadingAcccessories"
import DashboardHeaderHeadingAccessoriesMobileWrapper from "./DashboardHeaderHeadingAccessoriesMobileWrapper"
import { MobileMenuDialog } from "./MobileMenuModal"
import { Notifications } from "./Notifications"
// import { useSidebarContext } from "./SidebarContext"
import { Input } from "@/components/ui/input";
// import SearchIcon from "@/app/icons/sidebar/SearchIcon"


export function DashboardHeader() {
  // const { toggleSidebar } = useSidebarContext()

  return (
    <>
      <header className="relative flex items-center justify-between gap-4 bg-main-solid px-6 pb-[.875rem] pt-6 md:bg-white md:px-7 lg:px-11 z-[1]">
        <div className="flex min-w-0 grow flex-wrap items-center gap-x-3 gap-y-1.5">
          {/* Sidebar Toggle Button - visible on desktop */}
          {/* <button
            aria-label="Toggle sidebar"
            className="mr-2 hidden rounded-md p-2 hover:bg-gray-100"
            onClick={toggleSidebar}
          >
            <Menu size={20} />
          </button> */}
           <div className="relative w-[629px] md:w-1/2 h-[44px]  border-[#667085] rounded-10 whitespace-nowrap">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-black h-4 w- " />
          <Input  placeholder="Search here..." className="pl-9 w-[629px] md:w-full h-[40px] bg-[#F9FAFB] border-0"/> 
          </div>  
          

          {/* <div  >
            <span className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <SearchIcon className="h-5 w-5 text-gray-500" />
            </span>
            <input
              type="text"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring focus:ring-blue-500"
              placeholder="Search..."
            />
          </div> */}

          {/* <DashboardHeaderHeading /> */}

          <div className="hidden md:block">
            <DashboardHeaderHeadingAcccessories />
          </div>
        </div>

        <div className="flex items-center gap-4">
          <LinkButton className='hidden' href="/dashboard" size="unstyled" variant="unstyled">
            <span className="sr-only">Go home</span>
            <svg fill="none" height={40} viewBox="0 0 40 40" width={40}>
              <circle className="fill-[#2D4696] md:fill-[#ECF1FF]" cx={20} cy={20} fill="#ECF1FF" r={20} />
              <path
                className="fill-white md:fill-main-solid"
                d="m27.37 15.254-5.28-3.694c-1.439-1.008-3.648-.953-5.032.12l-4.593 3.583c-.916.715-1.64 2.182-1.64 3.337v6.325a4.245 4.245 0 0 0 4.234 4.244h9.882a4.237 4.237 0 0 0 4.235-4.235V18.72c0-1.237-.797-2.759-1.806-3.465Zm-6.682 10.248a.692.692 0 0 1-.688.688.692.692 0 0 1-.687-.688v-2.75c0-.375.311-.687.687-.687.376 0 .688.312.688.687v2.75Z"
                fill="#032180"
              />
            </svg>
          </LinkButton>

          <div className="hidden md:flex md:gap-4">

            <Notifications />
            <DashboardHeaderProfileMenu />
          </div>

          <MobileMenuDialog />
        </div>
      </header>

      <DashboardHeaderHeadingAccessoriesMobileWrapper>
        <DashboardHeaderHeadingAcccessories />
      </DashboardHeaderHeadingAccessoriesMobileWrapper>
    </>
  )
}
