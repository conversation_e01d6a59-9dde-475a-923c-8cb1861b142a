'use client'

import React, { useState } from 'react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/core'
import { useProductUsageChart, type ProductUsagePeriod } from '../api/ProductUsageChart'
import Doughnut<PERSON><PERSON> from './DoughnutChart'

export default function ProductUsage() {
  // State to track the selected period
  const [selectedPeriod, setSelectedPeriod] = useState<string>('today')

  // Map select values to API parameter values
  const getApiPeriod = (selectValue: string): ProductUsagePeriod => {
    const periodMap: Record<string, ProductUsagePeriod> = {
      'today': 'today',
      'this-Week': 'this_week',
      'this-month': 'this_month',
      'six-months': 'six_months',
      'this-year': 'this_year',
      'all-time': 'all_time'
    };
    return periodMap[selectValue] || 'today';
  };

  // Convert select value to API period and fetch data
  const apiPeriod = getApiPeriod(selectedPeriod)
  const { data: productUsageResponse, isLoading } = useProductUsageChart(apiPeriod)

  console.log('Selected period:', selectedPeriod)
  console.log('API period:', apiPeriod)
  console.log('Product Usage response:', productUsageResponse)

  // Handle select change
  const handleSelectChange = (value: string) => {
    setSelectedPeriod(value)
  }

  // Get description text based on selected period
  const getDescriptionText = (period: string) => {
    switch (period) {
      case 'today':
        return 'Today\'s Transactions'
      case 'this-Week':
        return 'This Week\'s Transactions'
      case 'this-month':
        return 'This Month\'s Transactions'
      case 'six-months':
        return 'Six Months Transactions'
      case 'this-year':
        return 'This Year\'s Transactions'
      case 'all-time':
        return 'All Time Transactions'
      default:
        return 'Today\'s Transactions'
    }
  }

  return (
    <div className='w-full h-full bg-[#ffffff] border-1 border-[#E9E9E9] rounded-lg p-6'>
      <div className="flex flex-col justify-between items-start gap-2 md:gap-8 mb-6 flex-wrap">
        <div className='flex items-center justify-between w-full'>
          <h2 className="text-lg font-medium">Product Usage</h2>
          <Select value={selectedPeriod} onValueChange={handleSelectChange}>
            <SelectTrigger className="lg:w-52 border-1 border-[#00000033] text-xs md:text-sm">
              <SelectValue placeholder="today" />
            </SelectTrigger>
            <SelectContent className="whitespace-nowrap">
              <SelectItem value="today" className="whitespace-nowrap">Today</SelectItem>
              <SelectItem value="this-Week" className="whitespace-nowrap">This Week</SelectItem>
              <SelectItem value="this-month" className="whitespace-nowrap">This Month</SelectItem>
              <SelectItem value="six-months" className="whitespace-nowrap">Six Month</SelectItem>
              <SelectItem value="this-year" className="whitespace-nowrap">This Year</SelectItem>
              <SelectItem value="all-time" className="whitespace-nowrap">All Time</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <DoughnutChart
          data={productUsageResponse}
          isLoading={isLoading}
          description={getDescriptionText(selectedPeriod)}
        />
      </div>
    </div>
  )
}
