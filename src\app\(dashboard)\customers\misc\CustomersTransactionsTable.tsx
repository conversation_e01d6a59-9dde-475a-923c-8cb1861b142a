"use client";

import { useState } from "react";
import { Search } from "lucide-react";
// import axios from "axios"

import { Button, Checkbox, Input } from "@/components/core";
import FilterLogo from "@/app/icons/sidebar/FilterLogo";
import ExportLogo from "@/app/icons/sidebar/ExportLogo";
import ActionPopover from "./ActionPopover";
import { useUserDetails } from "../../dashboard/misc/api/UserDetails";
// import DotLogo from "@/app/icons/sidebar/DotLogo";
// import ActionModal from "./ActionModal";

// Sample data for the transactions
const transactions = [
  {
    id: "LB-384",
    customerName: "Annette <PERSON>",
    email: "<EMAIL>",
    phoneNumber: "**********",
    KYCUpdate: "Verified",
    accountStatus: "Active",
    joined: "Nov, 15, 2024",
    transactions: "₦325,000",
  },
  // {
  //   id: "LB-898",
  //   customerName: "<PERSON>",
  //   email: "<EMAIL>",
  //   phoneNumber: "**********",
  //   KYCUpdate: "Not Verified",
  //   accountStatus: "Active",
  //   joined: "Nov, 15, 2024",
  //   transactions: "₦325,000",
  // },
];

export default function CustomersTransactionsTable() {
  // const [currentPage, setCurrentPage] = useState(1)
  const [customerDetails, setCustomerDetails] = useState({
    customer_id: '',
    customer_name: "",
  })
  const [selectedTransactions, setSelectedTransactions] = useState<number[]>(
    []
  );

  const toggleSelectAll = () => {
    if (selectedTransactions.length === transactions.length) {
      setSelectedTransactions([]);
    } else {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //@ts-ignore
      setSelectedTransactions(transactions.map((t) => t.id));
    }
  };

  const toggleSelect = (id: number) => {
    if (selectedTransactions.includes(id)) {
      setSelectedTransactions(selectedTransactions.filter((t) => t !== id));
    } else {
      setSelectedTransactions([...selectedTransactions, id]);
    }
  };


  const { data: userDetailsResponse } = useUserDetails();
  console.log(userDetailsResponse?.data, "resp")

  return (
    <div className="min-h-screen  text-black ">
      <div className="w-full mx-auto space-y-8 ">
        {/* Transactions Table */}

        <div className="bg-white border-blue-800 rounded-lg p-6 overflow-hidden ">
          <div className="py-4 flex justify-between items-center flex-wrap">
            <div className="flex items-center gap-2">
              <div className="relative   h-[44px] border-[0.4px] border-[#00000033] rounded-[8px] whitespace-nowrap">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-black h-4 w-  " />
                <Input
                  placeholder="Search a transaction"
                  className="pl-9 bg-inherit  text-black focus:border-[#F9FAFB]"
                />
              </div>
              <Button className="bg-white border-[0.4px] border-[#00000033] rounded-[8px] text-black hover:bg-[#F9FAFB]  h-[44px] gap-2 font-medium text-sm">
                <FilterLogo />
                Filter
              </Button>
            </div>

            <div className="flex items-center gap-2">
              <Button className="bg-white border-[0.4px] border-[#00000033] rounded-[8px] text-black hover:bg-[#F9FAFB] h-[44px] gap-2 font-medium text-sm">
                <ExportLogo />
                Export
              </Button>
            </div>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-[#F9FAFB] text-black">
                <tr className="divide-x divide-[#E2E8F0]">
                  <th className="px-4 pl-10 py-3 text-left">
                    <Checkbox
                      checked={
                        selectedTransactions.length === transactions.length
                      }
                      onCheckedChange={toggleSelectAll}
                    />
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium">
                    ID
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium">
                    Customer Name
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium">
                    Email
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium">
                    Phone Number
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium">
                    KYC Status
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium">
                    Account Status
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium">
                    Joined
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium">
                    Transactions
                  </th>
                  <th className="px-4 py-3 text-left text-sm font-medium">
                    Action
                  </th>
                </tr>
              </thead>
              <tbody className="border divide-y d divide-[#E2E8F0]">

                {
                  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
                  //@ts-ignore
                  userDetailsResponse?.data?.map(({ basic_info, account_info }) => {
                    console.log(basic_info, account_info)
                    return (
                      <>
                        <tr key={account_info?.id} className="divide-x divide-[#E2E8F0] hover:bg-[#F9FAFB]">
                          <td className="px-4 pl-10 py-1">
                            <Checkbox
                              checked={selectedTransactions.includes(account_info?.id)}
                              onCheckedChange={() => toggleSelect(account_info?.id)}
                            />
                          </td>
                          <td className="px-4 py-1 text-sm opacity-85 ">
                            {basic_info?.id}
                          </td>
                          <td className="px-4 py-1 text-sm opacity-85">
                            {basic_info?.full_name}
                          </td>
                          <td className="px-4 py-1 text-sm opacity-85">
                            {basic_info?.email}
                          </td>
                          <td className="px-4 py-1 text-sm opacity-85">
                            {basic_info?.phone_number}
                          </td>
                          <td className="px-4 py-1 text-sm opacity-85">
                            {basic_info?.kyc_status}
                          </td>
                          <td className="px-4 py-1 text-sm opacity-85">
                            {account_info?.account_status}
                          </td>
                          <td className="px-4 py-1 text-sm opacity-85">
                            {account_info?.date_created}
                          </td>
                          <td className="px-4 py-1 text-sm opacity-85">
                            {account_info?.account_type}
                          </td>
                          <td className="px-4 py-1 ">
                            <button
                              onClick={() => {
                                console.log("hghhjghjg", account_info, "m")
                                setCustomerDetails({
                                  customer_id: basic_info?.id,
                                  customer_name: basic_info.customerName,
                                });
                              }}
                              className={`px-1 py-1 rounded-10 text-xs font-medium   `}
                            >
                              <ActionPopover customer_id={customerDetails.customer_id} customer_name={customerDetails.customer_name} />
                              {/* <ActionModal customer_id="" customer_name="" /> */}
                            </button>
                          </td>
                        </tr>
                      </>
                    )
                  }
                  )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
