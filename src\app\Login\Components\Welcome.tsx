"use client";
import { useState } from "react";
import React from "react";
import Link from "next/link";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import VisibleSVG from "./VisibleSVG";
import NoVisibleSVG from "./NotVisibleSVG";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { SubmitHandler,useForm } from "react-hook-form"
import { useRouter } from "next/navigation";
import { formatAxiosErrorMessage } from "@/utils/errors";
import { AxiosError } from "axios";
import { useAuthUserDetails } from "../api/authUserDetails";
import { useErrorModalState } from "@/hooks";
import { Button, ErrorModal } from "@/components/core";
import { Loader2 } from "lucide-react";


const contactSchema = z.object({
  email: z.string().email("Invalid email address"),

  password: z
    .string()
    .min(6, { message: "Password must be at least 8 characters long." }),
  // .max(8, { message: "Password must be at most 8 characters long." }),
});
export type userStatusType = z.infer<typeof contactSchema>;

export default function Welcome() {
  const [visible, setVisible] = useState(false);
  const router = useRouter()

  const { mutate: submitForm } = useAuthUserDetails();

   const [isLoading, setIsLoading] = useState(false);

   

  // const [error, setError] = useState(false);
  // const [errorMessage, setErrorMessage] = useState("");

  const {
    isErrorModalOpen,
    setErrorModalState,
    closeErrorModal,
    openErrorModalWithMessage,
    errorModalMessage,
} = useErrorModalState();

  const {
    register,
    handleSubmit,
    // setError,
    formState: { errors, isSubmitting },
  } = useForm<userStatusType>({
    resolver: zodResolver(contactSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });


  const onSubmit: SubmitHandler<userStatusType> = async (data) => {
   
setIsLoading(true)

   console.log(data);
    const updateData ={
      ...data,
      device_type: "MOBILE"
    }
    submitForm(updateData,
      {
        onSuccess: (data) => {
          console.log("success", data);
          router.push("/dashboard");
        },
       onError(error) {
          console.log("error", error);
          if (error instanceof AxiosError) {
            const errorMessage = formatAxiosErrorMessage(error);
            openErrorModalWithMessage(errorMessage as string);
          } else {
            openErrorModalWithMessage(
              "An unexpected error occurred. Please try again."
            );
          }
       },
      }
    )
   
    // try {
    //   await new Promise((resolve) => setTimeout(resolve, 1000)); // Simulate a delay
    //   console.log('success', data);
    //   router.push('/dashboard')
    // } catch (error) {
    //   console.error("Error:", error);{
    //     setError("email", {
    //       message: "Invalid email or password",
    //     });
    //   };
    // }
  };
  // const onSubmit = (data: userStatusType) => {


  return (
    <div className="flex  items-center justify-center bg-gray-100 h-screen py-8 sm:py-0 ">
      <div className="flex flex-col items-start justify-start bg-white w-[350px] h-[565px] md:w-[456px] md:h-[540px] py-8 px-7 rounded-lg shadow-lg">
        <div className="flex flex-col items-start justify-start w-full">
          <p className="text-[#101928] font-semibold text-[28px]">
            Welcome Back!
          </p>
          <span className="text-[#667185] font-normal text-[16px] ">
            Please enter your login details to continue.
          </span>
        </div>

        {/* form */}
        <div className="flex flex-col items-start justify-start w-full  ">
          <form className=" mt-[28px] w-full" action="" onSubmit={handleSubmit(onSubmit)}>
            
            {/* Email Address */}
            <div className="flex flex-col mb-[16px] w-full">
              <label className="mb-[4px] mt-[8px] text-[14px] font-medium text-[#101928] ">
                Email Address
              </label>

              
              <Input
                className={`${
                  errors?.email
                    ? "border border-red-700"
                    : "border "
                } border-[1px] border-[#D0D5DD] py-1.5 px-[20px] rounded-[8px] text-[14px] h-[56px] w-full`}
                type="text"
                placeholder="Enter your email address"
                id="email"
                disabled={isLoading}
                {...register("email")}
              />
              {errors?.email && (
                <p className="text-red-600 text-[12px]">{errors?.email?.message}</p>
              )}
            </div>

            {/* Password */}
            <div className="flex flex-col mb-[16px] w-full relative">
              <label className="mb-[4px]  text-[14px] font-medium text-[#101928]">
                Password
              </label>
              <Input
                className={`${
                  errors?.password
                    ? "border border-red-700"
                    : "border "
                }border-[1px] border-[#D0D5DD] py-1.5 px-[20px] rounded-[8px] text-[14px] h-[56px] w-full`}
                type={visible ? "text" : "password"}
                placeholder="Enter your password"
                id="password"
                disabled={isLoading}
                {...register("password")}
              />

              <div className="p-2 absolute  left-9/10 text-amber-800 opacity-30">
                {/* {visible ? <EyeVisible onClick={()=>setVisible(!visible)} className="absolute top-4 right-4 cursor-pointer"/> : <EyeInvisible onClick={()=>setVisible(!visible)} className="absolute top-4 right-4 cursor-pointer"/>} */}
                <button 
                  type="button"
                  onClick={() => setVisible(!visible)}
                  className="absolute top-[45px]  cursor-pointer"
                >
                  {visible ? <VisibleSVG /> : <NoVisibleSVG />}
                </button>
              </div>

              {errors?.password && (
                <p className="text-red-600 text-[12px]">{errors?.password?.message}</p>
              )}
            </div>
          

          {/* Remember me and Forgot Password */}
          <div className="flex justify-between items-center pt-2 w-full">
            <div className="flex items-center">
              {/* <IoIosCheckboxOutline className='size-6 ' /> */}
              <Checkbox className=" border-[1.5px]" />
              <span className="text-[12px] font-medium pl-2">
                Remember me for 7 days
              </span>
            </div>
            <div className="flex items-center ">
              <Link
                href="/onboarding/ForgotPassword"
                className="text-[14px] font-medium text-[#032282]"
              >
                Forgot Password?
              </Link>
            </div>
          </div>

          <div className="flex flex-col items-center justify-center w-full">
            <button disabled={isLoading}
              type="submit"
              
              
              className="flex border-[0.5px] bg-[#032282]  justify-center items-center py-1.5 mt-[32px] rounded-[10px] text-white text-[16px] w-full h-[56px] cursor-pointer"
              // onClick={() => router.push("/Dashboards")}
            >

              {isLoading ? (
                <span className="flex items-center justify-center gap-2">
                  <Loader2 className="w-4 h-4 animate-spin" /> Logging in...
                </span>
              ) : (
                'Login'
              )}
              {/* {isLoading? "Loading...." : "Log into Account"} */}
              {/* Log into Account */}
            </button>
          </div>

          <div className="flex flex-col items-center justify-center w-full">
            <button
              className=" flex justify-center items-center py-1.5 mt-[26px] rounded-[10px]  text-[14px] cursor-pointer"
              // onClick={() => router.push("/onboarding")}
            >
              <p className="text-[12px] font-medium text-[#98A2B3]">
                Are you new here?
                <span className="text-[14px] font-medium text-[#032282] px-2">
                  Create Account
                </span>
              </p>
            </button>
          </div>
          </form>
        </div>
      </div>

      <ErrorModal
                isErrorModalOpen={isErrorModalOpen}
                setErrorModalState={setErrorModalState}
                subheading={
                    errorModalMessage || 'Please check your inputs and try again.'
                }
            >
                <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                    <Button
                        className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
                        size="lg"
                        type="button"
                        onClick={closeErrorModal}
                    >
                        Okay
                    </Button>
                </div>
            </ErrorModal>
    </div>
  );
}

