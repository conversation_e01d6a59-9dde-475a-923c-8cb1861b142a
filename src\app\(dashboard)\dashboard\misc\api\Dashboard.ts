import { adminAxios } from "@/lib/axios";

import { useQuery } from "@tanstack/react-query";

// Define the period type
export type DashboardPeriod = 'today' | 'this_week' | 'this_month' | 'all_time';

// Map tab values to API parameter values
const periodMap: Record<string, DashboardPeriod> = {
  'today': 'today',
  'this-week': 'this_week',
  'this-month': 'this_month',
  'all-time': 'all_time'
};

export const getDashboard = async (period: DashboardPeriod = 'today') => {
    const response = await adminAxios.get(
        `/customer_care_management_system/dashboard/?period=${period}`
    );
    
    return response.data;
};

export const useDashboard = (period: DashboardPeriod = 'today') => {
    return useQuery({
        queryKey: ['dashboard', period],
        queryFn: () => getDashboard(period),
    });
};

// Helper function to convert tab value to API period
export const getApiPeriod = (tabValue: string): DashboardPeriod => {
    return periodMap[tabValue] || 'today';
};
