'use client';

import * as React from 'react';

import {
    <PERSON>alog,
    DialogBody,
    <PERSON>alogClose,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/core';
import { useBooleanStateControl } from '@/hooks';

export function Notifications() {
    const {
        state: isModalOpen,
        setState: setModalState,
        // setFalse: closeModal,
    } = useBooleanStateControl();

    // Use state to avoid hydration issues
    const [companyId, setCompanyId] = React.useState('');

    // Use effect to safely access searchParams after hydration
    React.useEffect(() => {
        const searchParams = new URLSearchParams(window.location.search);
        const id = searchParams.get('companyId') || searchParams.get('company') || '';
        setCompanyId(id);
    }, []);

    // useRouteChangeEvent(() => closeModal());

    return (
        <Dialog open={isModalOpen} onOpenChange={setModalState}>
            <DialogTrigger disabled={!companyId} size="unstyled" variant="unstyled">
                <div className="relative">
                    <svg
                        fill="none"
                        height={40}
                        viewBox="0 0 40 40"
                        width={40}
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <circle cx={20} cy={20} fill="#ECF1FF" r={20} />
                        <path
                            d="M28.4 24.33c-.28.75-.87 1.32-1.64 1.58-1.08.36-2.19.63-3.31.82-.11.02-.22.04-.33.05-.18.03-.36.05-.54.07-.22.03-.45.05-.68.07-.63.05-1.25.08-1.88.08-.64 0-1.28-.03-1.91-.09-.27-.02-.53-.05-.79-.09l-.44-.06c-.11-.02-.22-.03-.33-.05-1.11-.18-2.21-.45-3.28-.81-.8-.27-1.41-.84-1.68-1.57-.27-.72-.17-1.56.26-2.28l1.13-1.88c.24-.41.46-1.2.46-1.68v-1.86c0-3.63 2.95-6.58 6.58-6.58 3.62 0 6.57 2.95 6.57 6.58v1.86c0 .48.22 1.27.47 1.68l1.13 1.88c.41.7.49 1.52.21 2.28Z"
                            fill="#07112D"
                        />
                        <path
                            d="M20 18.76a.76.76 0 0 1-.76-.76v-3.1a.76.76 0 1 1 1.52 0V18c-.01.42-.35.76-.76.76Z"
                            fill="#fff"
                        />
                        <path
                            d="M22.83 28.01A3.014 3.014 0 0 1 20 30c-.79 0-1.57-.32-2.12-.89-.32-.3-.56-.7-.7-1.11.13.02.26.03.4.05.23.03.47.06.71.08.57.05 1.15.08 1.73.08.57 0 1.14-.03 1.7-.08.21-.02.42-.03.62-.06l.49-.06Z"
                            fill="#07112D"
                        />
                    </svg>

                </div>
            </DialogTrigger>
            <DialogContent className="!fixed !right-0 top-0 max-w-md !my-0 !rounded-none h-screen">
                <DialogHeader className="gap-4 !rounded-none">
                    <DialogTitle className="max-w-[14.4375rem] font-normal">
                        Notifications
                    </DialogTitle>

                    <DialogClose className="rounded-full p-0">
                        <svg
                            fill="none"
                            height={30}
                            viewBox="0 0 30 30"
                            width={30}
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <circle cx={15} cy={15} fill="#ff0000" fillOpacity={0.2} r={15} />
                            <path
                                d="m11.68 12.319 6.639 5.362m-6 .638 5.362-6.638"
                                stroke="#ff0000"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                            />
                        </svg>
                    </DialogClose>
                </DialogHeader>

                <DialogBody className="px-4">


                </DialogBody>
            </DialogContent>
        </Dialog>
    );
}
