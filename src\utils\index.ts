const TOKEN_STORAGE_PREFIX = 'LIBERTY_CRM_';

export const tokenStorage = {
  // getToken: () => JSON.parse(

  //     window.localStorage.getItem(`${TOKEN_STORAGE_PREFIX}TOKEN`) as string,
  // ),

  getToken:() =>{
    if(typeof window === 'undefined')return null
    const token = window.localStorage.getItem(`${TOKEN_STORAGE_PREFIX}TOKEN`) 
    return token ? JSON.parse(token) : null
  },


  setToken: (token: string) => {
    if(typeof window  === 'undefined')return;
    window.localStorage.setItem(
      `${TOKEN_STORAGE_PREFIX}TOKEN`,
      JSON.stringify(token),
    );
  },
  
  clearToken: () => {
    if(typeof window === 'undefined')return;
    window.localStorage.removeItem(`${TOKEN_STORAGE_PREFIX}TOKEN`);
  },
  // setReferral: (data: string) => {
  //   window.localStorage.setItem(
  //     `${TOKEN_STORAGE_PREFIX}REFERRAL`,
  //     JSON.stringify(data),
  //   );
  // },
  // getReferral: () => JSON.parse(
  //   window.localStorage.getItem(`${TOKEN_STORAGE_PREFIX}REFERRAL`) as string,
  // ),
  // clearReferral: () => {
  //   window.localStorage.removeItem(`${TOKEN_STORAGE_PREFIX}REFERRAL`);
  // },
};
