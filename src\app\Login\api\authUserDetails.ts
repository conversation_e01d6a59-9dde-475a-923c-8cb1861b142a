



import { adminAxios, setAxiosDefaultToken } from '@/lib/axios';


import type { AxiosResponse } from 'axios';

import { useMutation } from '@tanstack/react-query';
import { useAuth } from '@/context/authentication';
import { getAuthenticatedUser } from './getAuthenticatedUser';
import { tokenStorage } from '@/utils';


export interface AuthUserDetails {
    email: string
    password: string
    device_type: string
}

interface TokenResponse{
  access:string;
  refresh:string
}

const login = (loginPhoneNumberDto: AuthUserDetails): Promise<AxiosResponse<TokenResponse>> =>
  adminAxios.post('/user/login/create/', loginPhoneNumberDto);



export const useAuthUserDetails = () => {
  const {authDispatch} = useAuth();

 return useMutation(login,{
  onSuccess: async ({data}) => {
    const {access:token} = data;
    
    // tokenStorage.setToken(token);
    //  setAxiosDefaultToken(token, adminAxios);

    tokenStorage.setToken(token);
    setAxiosDefaultToken(token, adminAxios);
     const user = await getAuthenticatedUser();

    if(authDispatch){
      authDispatch({type:"LOGIN", payload:user});
      authDispatch({type:"STOP_LOADING"})
    }
  }
 })
}
