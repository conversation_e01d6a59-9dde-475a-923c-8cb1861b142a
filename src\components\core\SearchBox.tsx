
import React from 'react';

export function SearchBox() {
  return (
    <div className="flex h-[42px] flex-row items-center rounded-lg border-[0.3px] border-solid border-[#D6D6D6] bg-white px-[18px] py-3 sm:w-[275px]">
      <svg
        fill="none"
        height={20}
        viewBox="0 0 20 20"
        width={20}
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M17.7656 16.6895L12.6934 11.6172C13.4805 10.5996 13.9062 9.35547 13.9062 8.04687C13.9062 6.48047 13.2949 5.01172 12.1895 3.9043C11.084 2.79687 9.61133 2.1875 8.04687 2.1875C6.48242 2.1875 5.00977 2.79883 3.9043 3.9043C2.79687 5.00977 2.1875 6.48047 2.1875 8.04687C2.1875 9.61133 2.79883 11.084 3.9043 12.1895C5.00977 13.2969 6.48047 13.9062 8.04687 13.9062C9.35547 13.9062 10.5977 13.4805 11.6152 12.6953L16.6875 17.7656C16.7024 17.7805 16.72 17.7923 16.7395 17.8004C16.7589 17.8084 16.7797 17.8126 16.8008 17.8126C16.8218 17.8126 16.8427 17.8084 16.8621 17.8004C16.8815 17.7923 16.8992 17.7805 16.9141 17.7656L17.7656 16.916C17.7805 16.9011 17.7923 16.8835 17.8004 16.864C17.8084 16.8446 17.8126 16.8238 17.8126 16.8027C17.8126 16.7817 17.8084 16.7609 17.8004 16.7414C17.7923 16.722 17.7805 16.7043 17.7656 16.6895ZM11.1406 11.1406C10.3125 11.9668 9.21484 12.4219 8.04687 12.4219C6.87891 12.4219 5.78125 11.9668 4.95312 11.1406C4.12695 10.3125 3.67187 9.21484 3.67187 8.04687C3.67187 6.87891 4.12695 5.7793 4.95312 4.95312C5.78125 4.12695 6.87891 3.67187 8.04687 3.67187C9.21484 3.67187 10.3145 4.125 11.1406 4.95312C11.9668 5.78125 12.4219 6.87891 12.4219 8.04687C12.4219 9.21484 11.9668 10.3145 11.1406 11.1406Z"
          fill="#556575"
        />
      </svg>
      <div className="ml-1 w-full">
        <input
          className="p-1 text-sm placeholder-[#556575] outline-none ring-0"
          name="psearch"
          placeholder="Search"
          type="text"
        />
      </div>
    </div>
  );
}
