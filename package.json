{"name": "crmm", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.2", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.14", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.6", "@tanstack/react-query": "^4.36.1", "@tanstack/react-table": "^8.21.3", "axios": "^1.9.0", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.10.0", "input-otp": "^1.4.2", "lucide-react": "^0.506.0", "next": "15.3.1", "nextjs-toploader": "^3.8.16", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-day-picker": "^9.6.7", "react-dom": "^19.0.0", "react-hook-form": "^7.56.1", "react-hot-toast": "^2.5.2", "react-otp-input": "^3.1.1", "react-pin-input": "^1.3.1", "react-use": "^17.6.0", "recharts": "^2.15.3", "tailwind-merge": "^3.2.0", "vaul": "^1.1.2", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "tw-animate-css": "^1.2.8", "typescript": "^5"}}