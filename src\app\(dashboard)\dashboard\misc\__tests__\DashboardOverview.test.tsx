import { render, screen, fireEvent } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { DasboardOverview } from '../DashboardOverview';

// Mock the API module
jest.mock('../api/Dashboard', () => ({
  useDashboard: jest.fn(),
  getApiPeriod: jest.fn((tabValue: string) => {
    const periodMap: Record<string, string> = {
      'today': 'today',
      'this-week': 'this_week',
      'this-month': 'this_month',
      'all-time': 'all_time'
    };
    return periodMap[tabValue] || 'today';
  }),
}));

const mockUseDashboard = require('../api/Dashboard').useDashboard;

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
    },
  },
});

const renderWithQueryClient = (component: React.ReactElement) => {
  const testQueryClient = createTestQueryClient();
  return render(
    <QueryClientProvider client={testQueryClient}>
      {component}
    </QueryClientProvider>
  );
};

describe('DashboardOverview', () => {
  beforeEach(() => {
    mockUseDashboard.mockReturnValue({
      data: {
        data: {
          total_customers: 100,
          total_customers_percentage_diff: 5.2,
          verified_customers: 80,
          verified_customers_percentage_diff: 3.1,
          active_customers: 75,
          active_customers_percentage_diff: 2.8,
          pending_verification: 20,
          pending_verification_percentage_diff: 1.5,
          total_transactions: 500,
          total_transactions_percentage_diff: 8.3,
          pending_transactions_within_3days: 15,
          pending_transactions_percentage_diff: 2.1,
        }
      },
      isLoading: false,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders dashboard with default "today" period', () => {
    renderWithQueryClient(<DasboardOverview />);
    
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Showing data for today')).toBeInTheDocument();
    expect(screen.getByText('Today')).toBeInTheDocument();
  });

  it('changes period when tab is clicked', () => {
    renderWithQueryClient(<DasboardOverview />);
    
    // Click on "This week" tab
    const thisWeekTab = screen.getByText('This week');
    fireEvent.click(thisWeekTab);
    
    // Check if description text changes
    expect(screen.getByText('Showing data for this week')).toBeInTheDocument();
  });

  it('calls API with correct period when tab changes', () => {
    renderWithQueryClient(<DasboardOverview />);
    
    // Initially should call with 'today'
    expect(mockUseDashboard).toHaveBeenCalledWith('today');
    
    // Click on "This month" tab
    const thisMonthTab = screen.getByText('This month');
    fireEvent.click(thisMonthTab);
    
    // Should call with 'this_month'
    expect(mockUseDashboard).toHaveBeenCalledWith('this_month');
  });

  it('displays loading state correctly', () => {
    mockUseDashboard.mockReturnValue({
      data: null,
      isLoading: true,
    });

    renderWithQueryClient(<DasboardOverview />);
    
    // Should show loading skeletons
    const loadingElements = screen.getAllByText(/Loading/i);
    expect(loadingElements.length).toBeGreaterThan(0);
  });
});
