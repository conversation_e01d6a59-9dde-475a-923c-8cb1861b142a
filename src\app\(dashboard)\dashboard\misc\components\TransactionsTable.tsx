"use client"



import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from "recharts"
import {  Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/core"


// Sample data for the chart
const chartData = [
  { name: "January", inflow: 25, outflow: 15 },
  { name: "February", inflow: 30, outflow: 20 },
  { name: "March", inflow: 35, outflow: 25 },
  { name: "April", inflow: 40, outflow: 30 },
  { name: "May", inflow: 30, outflow: 20 },
  { name: "June", inflow: 25, outflow: 15 },
  { name: "July", inflow: 35, outflow: 25 },
  { name: "August", inflow: 40, outflow: 30 },
  { name: "September", inflow: 45, outflow: 35 },
  { name: "October", inflow: 50, outflow: 40 },
  { name: "November", inflow: 45, outflow: 35 },
  { name: "December", inflow: 55, outflow: 45 },
]

export default function FinancialDashboard() {
  // const [currentPage, setCurrentPage] = useState(1)
  // const [selectedTransactions, setSelectedTransactions] = useState<number[]>([])

  // const toggleSelectAll = () => {
  //   if (selectedTransactions.length === transactions.length) {
  //     setSelectedTransactions([])
  //   } else {
  //     setSelectedTransactions(transactions.map((t) => t.id))
  //   }
  // }

  // const toggleSelect = (id: number) => {
  //   if (selectedTransactions.includes(id)) {
  //     setSelectedTransactions(selectedTransactions.filter((t) => t !== id))
  //   } else {
  //     setSelectedTransactions([...selectedTransactions, id])
  //   }
  // }

 

  return (
    <div className="min-h-screen  text-white ">
      <div className="w-full mx-auto space-y-8 ">
       
        
        

        {/* Chart Section */}
        <div className="bg-[#0B1739] rounded-lg shadow-xl p-6">
          <div className="flex justify-between items-center gap-2 md:gap-8 mb-6 flex-wrap">
            <h2 className="text-lg font-medium">Transactions</h2>
            <div className="flex items-center gap-2 lg:gap-10">
              <div className="flex items-center gap-2">
                <span className="inline-block w-3 h-2 md:h-3 rounded-full bg-emerald-400"></span>
                <span className="text-xs md:text-sm">Total Inflow - ₦112,190,000</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="inline-block w-3 h-2 md:h-3 rounded-full bg-blue-400"></span>
                <span className="text-xs md:text-sm">Total Outflow - ₦112,190,000</span>
              </div>
            </div>

            <Select defaultValue="jan-dec-2024">
              <SelectTrigger className=" lg:w-52 bg-blue-950/50 border-blue-700 text-xs md:text-sm">
                <SelectValue placeholder="Select date range" />
              </SelectTrigger>
              <SelectContent className="whitespace-nowrap">
                <SelectItem value="jan-dec-2024" className="whitespace-nowrap" >Jan 2024 - Jun 2024</SelectItem>
                <SelectItem value="jul-dec-2024" className="whitespace-nowrap">Jul 2024 - Dec 2024</SelectItem>
              </SelectContent>
            </Select>

          </div>

          <div className="h-80 bg-[#122251]">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={chartData} margin={{ top: 30, right: 30, left: 20, bottom: 10 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#1e3a8a" vertical={false} horizontal={true}/>
                <XAxis dataKey="name" axisLine={false} tickLine={false} tick={{ fill: "#94a3b8", fontSize: 12 }}  />
                <YAxis
                  axisLine={false}
                  tickLine={true}
                  tick={{ fill: "#94a3b8", fontSize: 12 }}
                  tickFormatter={(value) => `${value}M`}
                />
                <Tooltip
                  contentStyle={{
                    backgroundColor: "#0f172a",
                    borderColor: "#1e40af",
                    borderRadius: "0.375rem",
                    color: "#f8fafc",
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="inflow"
                  stroke="#10b981"
                  strokeWidth={3}
                  dot={false}
                  activeDot={{ r: 6, fill: "#10b981" }}
                />
                <Line
                  type="monotone"
                  dataKey="outflow"
                  stroke="#3b82f6"
                  strokeWidth={3}
                  dot={false}
                  activeDot={{ r: 6, fill: "#3b82f6" }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    </div>
  )
}

