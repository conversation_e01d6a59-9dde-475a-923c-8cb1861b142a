import { Button, Input } from "@/components/core";
import {
  Dialog,
  DialogContent,
  // DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
// import { Input } from "@/components/ui/input";
import { Label } from "@radix-ui/react-label";

export function CreateAccountModal() {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <div className="basis-[50%] flex justify-start">
        <Button className="bg-[#0322820A] border-[0.4px] border-[#032282] text-[#032282] rounded-xl text-[10px] py-1 px-2">
          Create Account
        </Button>
</div>
        {/* <div className=" bg-[#0322820A] border-[0.4px] border-[#032282] text-[#032282] rounded-xl text-[10px] py-1 px-2">Create Account</div> */}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[452px] pb-5 pt-0 px-1  rounded-[20px] ">
        <DialogHeader>
          <DialogTitle className="flex items-center font-medium text-[16px] border-b-[0.8px] border-[#E9EBEE] pt-[14px] pb-[14px] px-[14px]">
            Create Account
          </DialogTitle>
           
          {/* <DialogDescription className="flex   py-[12px] px-[24px] ">
           
            <div>
              <p className="text-[12px]  font-normal">Amount</p>
              <p className="text-[24px] font-semibold">₦10,000</p>
            </div>
            <div className="justify-end">
              <div className="flex items-center justify-end gap-1">
                <div className="size-1.5 bg-[#EEBE3E] rounded-full" />
                <p className=" text-[12px]  font-normal">Pending</p>
              </div>
              <p className="text-[12px] font-normal">
                Tuesday, 29th March, 2025
              </p>
            </div>
          </DialogDescription> */}

          {/* Amount */}
        </DialogHeader>
        <div className="max-h-[700px] overflow-auto no-scrollbar">
          

            
        <div className="">

          {/* Overview */}
          <div className="flex flex-col py-[px] px-[14px] gap-1">
            <Label className="text-[14px] font-normal  ">Registered Business Name</Label>
            <Input
              id="name"
              defaultValue=""
              className="col-span-3 border-[1px] border-[#E9EBEE] rounded-[10px] h-[44px]"
            />
            
          </div>


          <div className="flex flex-col py-[10px] px-[14px] gap-1">
            <Label className="text-[14px] font-normal ">Company RC Number</Label>
            <Input
              id="name"
              defaultValue=""
              className="col-span-3 border-[1px] border-[#E9EBEE] rounded-[10px] h-[44px]"
            />
            
          </div>


          <div className="flex flex-col py-[10px] px-[14px] gap-1">
            <Label className="text-[14px] font-normal ">Incorporation Date</Label>
            <Input
              id="name"
              defaultValue=""
              className="col-span-3 border-[1px] border-[#E9EBEE] rounded-[10px] h-[44px]"
            />
            
          </div>


          <div className="flex flex-col py-[10px] px-[14px] gap-1">
            <Label className="text-[14px] font-normal ">TIN Number</Label>
            <Input
              id="name"
              defaultValue=""
              className="col-span-3 border-[1px] border-[#E9EBEE] rounded-[10px] h-[44px]"
            />
            
          </div>


          <div className="flex flex-col py-[10px] px-[14px] gap-1">
            <Label className="text-[14px] font-normal ">Corporate ID (Backend)</Label>
            <Input
              id="name"
              defaultValue=""
              className="col-span-3 border-[1px] border-[#E9EBEE] rounded-[10px] h-[44px]"
            />
            
          </div>


          <div className="flex flex-col py-[10px] px-[14px] gap-1">
            <Label className="text-[14px] font-normal ">Upload CAC Certificate</Label>
            <div className="justify-center items-center text-center">
            <Input
              id="file"
              type="file"
              defaultValue=""
              className="col-span-3 border-dashed border-[2px] text-[#4A4A68] text-center justify-center items-center border-[#E9EBEE] rounded-[10px] h-[140px]"
            />
            </div>
          </div>


          


          
          
        </div>

        <DialogFooter className="flex flex-col pt-[10px] px-[14px] gap-1">
          <Button type="submit" className="w-full h-[44px] bg-[#032282] ">Submit for review</Button>
        </DialogFooter>
        </div>
        
      </DialogContent>
    </Dialog>
  );
}
