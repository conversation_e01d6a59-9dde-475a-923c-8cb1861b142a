

import { useQuery } from '@tanstack/react-query';


import { adminAxios } from '@/lib/axios';
import { UserDataTypes } from '.';

export interface NoPinError {
  error: string;
  message: string;
  create_transaction_pin_link: string;
}



export const getAuthenticatedUser = async () => {
  const response  = await adminAxios.get(`/agency/user/get_user_details/`);
  return response.data as UserDataTypes;
};


export const useUser =() => {
  return useQuery({
    queryKey: ["user-details"],
    queryFn: getAuthenticatedUser,
    staleTime:0,
    refetchOnWindowFocus:true,
    refetchOnMount:true
  })

}
// export const useUser = () =>
//   useQuery("user-details", getAuthenticatedUser, { retry: 2 });