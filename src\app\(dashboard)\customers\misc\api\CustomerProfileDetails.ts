import { adminAxios } from "@/lib/axios";
import { useQuery } from "@tanstack/react-query";

// import axios from "axios"

export interface CustomerProfileData {
  status: string
  message: string
  data: Data
}

export interface Data {
  basic_info: BasicInfo
  account_info: AccountInfo
  wallets: Wallet[]
  overall_financial_summary: OverallFinancialSummary
}

export interface BasicInfo {
  id: number
  full_name: string
  email: string
  phone_number: string
  gender: string
  kyc_status: string
  address: string
  profile_picture: string
}

export interface AccountInfo {
  customer_id: string
  account_status: string
  account_type: string
  date_created: string
  last_login_date: string
}

export interface Wallet {
  wallet_info: WalletInfo
  linked_accounts: LinkedAccount[]
  financial_summary: FinancialSummary
}

export interface WalletInfo {
  wallet_id: string
  wallet_type: string
  wallet_type_display: string
  available_balance: number
  hold_balance: number
  is_active: boolean
  date_created: string
  last_updated: string
}

export interface LinkedAccount {
  account_id: string
  account_number: string
  account_name: string
  bank_name: string
  bank_code: string
  account_type: string
  account_type_display: string
  true_account_type: string
  true_account_type_display: string
  available_balance: number
  is_active: boolean
  date_created: string
  last_updated: string
}

export interface FinancialSummary {
  total_transactions_count: number
  total_transactions_value: number
  pending_transactions_count: number
  pending_transactions_value: number
  failed_transactions_count: number
  failed_transactions_value: number
  successful_credits_count: number
  successful_credits_value: number
  successful_debits_count: number
  successful_debits_value: number
  current_wallet_balance: number
  last_transaction_date?: string
  last_transaction_amount?: number
  last_transaction_type?: string
  last_transaction_status?: string
  last_transaction_reference?: string
}

export interface OverallFinancialSummary {
  total_transactions_count: number
  total_transactions_value: number
  pending_transactions_count: number
  pending_transactions_value: number
  failed_transactions_count: number
  failed_transactions_value: number
  successful_credits_count: number
  successful_credits_value: number
  successful_debits_count: number
  successful_debits_value: number
  current_wallet_balance: number
}



interface FetchCustomerProfile {
  user_id: string;
}

export const getCustomerProfileDetails = async ({user_id}:FetchCustomerProfile) => {
    const response =await adminAxios.get(
        `/customer_care_management_system/customers_profile_detail?user_id=${user_id}`

    );
    return response.data as CustomerProfileData;
};


export const useCustomerProfileDetails = (
     fetchOptions: FetchCustomerProfile
) => {
    return useQuery({
        queryKey: ['khfjh', fetchOptions],
        queryFn: () => getCustomerProfileDetails(fetchOptions as FetchCustomerProfile),
    
    });
}