"use client";

import React  from "react";
import Image from "next/image";
import { But<PERSON> } from "@/components/core";
import ArrowHeadDownLogo from "@/app/icons/sidebar/ArrowHeadDownLogo";
import { useCustomerProfileDetails } from "../api/CustomerProfileDetails";
import { useBillsTransactionDetails } from "../api/BillsTransaction";
// import { useTransactionsDetails } from "../api/TransactionsDetails";

interface CustomerDetails {
customer_id: string
}
export default function CustomerDetailsOverview({customer_id}: CustomerDetails) {

  const FetchCustomerProfile  = {
    user_id: customer_id,
  }

  const{data: customerData} = useCustomerProfileDetails(FetchCustomerProfile)
console.log(customerData?.data.basic_info.full_name, "customerData")


// const FetchBillsTransaction  = {
//     bill_id: customer_id,
//   }

 const{data: billsData} = useBillsTransactionDetails()
 // eslint-disable-next-line @typescript-eslint/ban-ts-comment
 //@ts-ignore
console.log(billsData?.data, "billsData")


  return (
    <div className="pb-3  ">
      <div className="flex justify-between items-center text-black  px-6 pt-6 gap-4 flex-wrap">
        <div className="flex ">
          <Image
            src="/images/Avatars3.png"
            alt="Customer Avatar"
            height={58}
            width={58}
            className="object-cover rounded-lg md:w-[58px] md:h-[58px]"
          />

          <div className="flex flex-col justify-center px-[14px]">
            <div className="flex items-center gap-2">
              <span className=" text-2xl font-semibold ">
         {customerData?.data.basic_info.full_name}
                {/* {user_name} */}
              </span>
              <div className="flex items-center justify-center  w-[61px] h-[20px] border-[0.4px] bg-[#DBFBEC99] border-[#06985566] rounded-[12px] gap-1  ">
                <div className="rounded-full w-1 h-1 bg-[#1BA019]"></div>
                <p className="text-[10px] font-normal text-[#475467] ">
                  Verified
                </p>
              </div>

              <div className="flex items-center justify-center  w-[61px] h-[20px] border-[0.4px] bg-white border-[#06985566] rounded-[12px] gap-1  ">
                <div className="rounded-full w-1 h-1 bg-[#1BA019]"></div>
                <p className="text-[10px] font-normal text-[#1BA019] ">
                  Active
                </p>
              </div>
            </div>

            <p className="text-[14px] font-normal text-[#667085]">
              {/* <EMAIL> */}
              {customerData?.data.basic_info.email}
            </p>
          </div>
        </div>
        <div className="bg-">
          <Button className="bg-[#032282] border-[0.4px]  rounded-[6px] text-white   h-[44px] gap-2 font-medium text-sm">
            Action
            <ArrowHeadDownLogo />
          </Button>
        </div>
      </div>
    </div>
  );
}
