'use client';

import * as React from 'react';
import { usePathname } from 'next/navigation';

import {
  Dialog,
  DialogBody,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  LinkButton,
} from '@/components/core';
import { useBooleanStateControl } from '@/hooks';

const quickAccessRoutes = [
  {
    name: 'Make requisitions',
    link: '/spend-management/make-requisitions',
  },
  {
    name: 'Record expenses',
    link: '/spend-management/expenses/record-expense/options',
  },
  { name: 'Pay salaries', link: '/payroll' },
  {
    name: 'Employee payroll dashboard',
    link: '/payroll/all-companies',
  },
  { name: 'Send money', link: '/send-money' },
  { name: 'Record sales', link: '/sales' },
  { name: 'Record stock', link: '/stock' },
  { name: 'Record invoice', link: '/invoices' },
];

const ALLOWED_PATHS = ['/dashboard']; // Add more paths as needed

export function QuickAccessModal() {
  const pathname = usePathname();
  const {
    state: isModalOpen,
    setState: setModalState,
  } = useBooleanStateControl();

  // useRouteChangeEvent(() => closeModal());

  // Don't render if not on allowed paths
  if (!ALLOWED_PATHS.includes(pathname)) {
    return null;
  }

  return (
    <Dialog open={isModalOpen} onOpenChange={setModalState}>
      <DialogTrigger className="fixed bottom-8 right-4 z-10 gap-2 rounded-full px-6 py-4 md:bottom-16 md:right-32">
        <span>
          <svg
            fill="none"
            height={24}
            viewBox="0 0 24 24"
            width={24}
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M9.32 13.28h3.09v7.2c0 1.06 1.32 1.56 2.02.76l7.57-8.6c.66-.75.13-1.92-.87-1.92h-3.09v-7.2c0-1.06-1.32-1.56-2.02-.76l-7.57 8.6c-.65.75-.12 1.92.87 1.92ZM8.5 4h-7m6 16h-6m3-8h-3"
              stroke="#fff"
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeMiterlimit={10}
              strokeWidth={1.5}
            />
          </svg>
        </span>

        <span className="font-wix-display text-sm font-medium">
          Quick access
        </span>
      </DialogTrigger>

      <DialogContent>
        <DialogHeader className="gap-4">
          <DialogTitle className="max-w-[14.4375rem] font-normal">
            Get quick access to top features on
            <span className="font-bold"> PayBox </span>with a single click.
          </DialogTitle>

          <DialogClose className="rounded-full p-0">
            <svg
              fill="none"
              height={30}
              viewBox="0 0 30 30"
              width={30}
              xmlns="http://www.w3.org/2000/svg"
            >
              <circle cx={15} cy={15} fill="#fff" fillOpacity={0.2} r={15} />
              <path
                d="m11.68 12.319 6.639 5.362m-6 .638 5.362-6.638"
                stroke="#fff"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </DialogClose>
        </DialogHeader>

        <DialogBody className="px-4">
          <nav>
            <ul className="flex flex-col gap-2.5">
              {quickAccessRoutes.map(({ name, link }) => {
                return (
                  <LinkButton
                    className="justify-between rounded-10 px-4 py-3.5"
                    href={link}
                    key={link}
                    size="unstyled"
                    variant="light"
                  >
                    <span className="text-left text-sm font-normal">
                      {name}
                    </span>

                    <svg
                      className="shrink-0"
                      fill="none"
                      height={21}
                      viewBox="0 0 26 21"
                      width={26}
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        clipRule="evenodd"
                        d="M17.424 7.834a3.456 3.456 0 0 1 .127 4.696l-.127.136-3.464 3.313a.832.832 0 0 1-1.19 0 .864.864 0 0 1-.07-1.127l.07-.08 3.465-3.314a1.728 1.728 0 0 0 .098-2.307l-.098-.109L12.77 5.73a.864.864 0 0 1 0-1.208.832.832 0 0 1 1.11-.07l.08.07 3.464 3.313Z"
                        fill="#032282"
                        fillRule="evenodd"
                      />
                      <path
                        clipRule="evenodd"
                        d="M12.378 7.834a3.456 3.456 0 0 1 .127 4.696l-.127.136-3.464 3.313a.832.832 0 0 1-1.19 0 .864.864 0 0 1-.07-1.127l.07-.08 3.465-3.314a1.728 1.728 0 0 0 .098-2.307l-.098-.109L7.724 5.73a.864.864 0 0 1 0-1.208.832.832 0 0 1 1.11-.07l.08.07 3.464 3.313Z"
                        fill="#032282"
                        fillRule="evenodd"
                        opacity={0.3}
                      />
                    </svg>
                  </LinkButton>
                );
              })}
            </ul>
          </nav>
        </DialogBody>
      </DialogContent>
    </Dialog>
  );
}
